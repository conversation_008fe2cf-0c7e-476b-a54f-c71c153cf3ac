{"contractName": "ValidatorRegistry", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "validator", "type": "address"}], "name": "ValidatorAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "validator", "type": "address"}], "name": "ValidatorRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "validator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}], "name": "ValidatorStatusChanged", "type": "event"}, {"constant": true, "inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "COMPANY_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "MAX_REPUTATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "MIN_REPUTATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "VALIDATOR_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "blacklistAccount", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isBlacklisted", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "pause", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "removeFromBlacklist", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "totalValidators", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "unpause", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "validatorActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "validatorJoinDate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "validatorReputation", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "validators", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_validator", "type": "address"}], "name": "addValidator", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_validator", "type": "address"}], "name": "removeValidator", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_validator", "type": "address"}, {"internalType": "bool", "name": "_active", "type": "bool"}], "name": "setValidatorStatus", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_validator", "type": "address"}, {"internalType": "uint256", "name": "_newReputation", "type": "uint256"}], "name": "updateValidatorReputation", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_validator", "type": "address"}], "name": "isValidator", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_validator", "type": "address"}], "name": "getValidatorInfo", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "reputation", "type": "uint256"}, {"internalType": "uint256", "name": "joinDate", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getActiveValidatorCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.5.17+commit.d19bba13\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"validator\",\"type\":\"address\"}],\"name\":\"ValidatorAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"validator\",\"type\":\"address\"}],\"name\":\"ValidatorRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"validator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"}],\"name\":\"ValidatorStatusChanged\",\"type\":\"event\"},{\"constant\":true,\"inputs\":[],\"name\":\"ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"COMPANY_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"MAX_REPUTATION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"MIN_REPUTATION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"PAUSER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"VALIDATOR_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_validator\",\"type\":\"address\"}],\"name\":\"addValidator\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"blacklistAccount\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getActiveValidatorCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_validator\",\"type\":\"address\"}],\"name\":\"getValidatorInfo\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"exists\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"reputation\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"joinDate\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"isBlacklisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_validator\",\"type\":\"address\"}],\"name\":\"isValidator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"removeFromBlacklist\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_validator\",\"type\":\"address\"}],\"name\":\"removeValidator\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_validator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"_active\",\"type\":\"bool\"}],\"name\":\"setValidatorStatus\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"totalValidators\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"_validator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_newReputation\",\"type\":\"uint256\"}],\"name\":\"updateValidatorReputation\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"validatorActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"validatorJoinDate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"validatorReputation\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"validators\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"A contract for managing a registry of validators with comprehensive access control.\",\"methods\":{\"addValidator(address)\":{\"details\":\"Adds a new validator to the registry with comprehensive validation.\",\"params\":{\"_validator\":\"The address of the validator to be added.\"}},\"blacklistAccount(address)\":{\"details\":\"Add address to blacklist\"},\"getActiveValidatorCount()\":{\"details\":\"Gets the total number of active validators.\",\"return\":\"The number of active validators.\"},\"getRoleAdmin(bytes32)\":{\"details\":\"Get the admin role that controls a role\"},\"getValidatorInfo(address)\":{\"details\":\"Gets validator information.\",\"params\":{\"_validator\":\"The address of the validator.\"},\"return\":\"exists, active, reputation, joinDate\"},\"grantRole(bytes32,address)\":{\"details\":\"Grant a role to an account\"},\"hasRole(bytes32,address)\":{\"details\":\"Check if an account has a specific role\"},\"isBlacklisted(address)\":{\"details\":\"Check if account is blacklisted\"},\"isValidator(address)\":{\"details\":\"Checks if an address is an active validator.\",\"params\":{\"_validator\":\"The address to be checked.\"},\"return\":\"A boolean indicating whether the address is an active validator.\"},\"owner()\":{\"details\":\"Get contract owner\"},\"pause()\":{\"details\":\"Pause the contract (emergency stop)\"},\"paused()\":{\"details\":\"Check if contract is paused\"},\"removeFromBlacklist(address)\":{\"details\":\"Remove address from blacklist\"},\"removeValidator(address)\":{\"details\":\"Removes a validator from the registry.\",\"params\":{\"_validator\":\"The address of the validator to be removed.\"}},\"renounceRole(bytes32,address)\":{\"details\":\"Renounce a role (caller renounces their own role)\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revoke a role from an account\"},\"setValidatorStatus(address,bool)\":{\"details\":\"Activates or deactivates a validator.\",\"params\":{\"_active\":\"The new active status.\",\"_validator\":\"The address of the validator.\"}},\"unpause()\":{\"details\":\"Unpause the contract\"},\"updateValidatorReputation(address,uint256)\":{\"details\":\"Updates validator reputation (can be called by market contract).\",\"params\":{\"_newReputation\":\"The new reputation score.\",\"_validator\":\"The address of the validator.\"}}},\"title\":\"ValidatorRegistry\"},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"project:/contracts/ValidatorRegistry.sol\":\"ValidatorRegistry\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/AccessControl.sol\":{\"keccak256\":\"0x9168705d35307f8203540f3740a77bf9a6a68b118a6bf24fa9b69f220f15b4fe\",\"urls\":[\"bzz-raw://e5d7ca7264c8ab5f61008d49b76528f1f36f57c7147da2e27643c1ebf4a452f5\",\"dweb:/ipfs/QmRK4biArKSyt9qpYCmKEN24HMfeDH9dELCcN1ye3wNiDb\"]},\"project:/contracts/ValidatorRegistry.sol\":{\"keccak256\":\"0x3dd637936deceaaa64379afb592272dfe66479ba97e70a414eb3faf81afcb782\",\"urls\":[\"bzz-raw://8882d26106f12b7f22563f250622c2221dba5c867a9b4ae414bc2c7ae65eabd6\",\"dweb:/ipfs/QmWTNFtAYHyzkXn7oiTZhB63s9vLqexTKfMNzScbsYrGpf\"]}},\"version\":1}", "bytecode": "0x608060405233600360016101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055506000600360006101000a81548160ff02191690831515021790555060016004819055506200007d6000801b336200029d60201b60201c565b620000c460405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a0190506040518091039020336200029d60201b60201c565b6200010b60405180807f5041555345525f524f4c45000000000000000000000000000000000000000000815250600b0190506040518091039020336200029d60201b60201c565b6200015560405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a01905060405180910390206000801b6200037c60201b60201c565b620001d160405180807f56414c494441544f525f524f4c45000000000000000000000000000000000000815250600e019050604051809103902060405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a01905060405180910390206200037c60201b60201c565b6200024d60405180807f434f4d50414e595f524f4c450000000000000000000000000000000000000000815250600c019050604051809103902060405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a01905060405180910390206200037c60201b60201c565b6200029760405180807f5041555345525f524f4c45000000000000000000000000000000000000000000815250600b01905060405180910390206000801b6200037c60201b60201c565b62000461565b620002af8282620003dd60201b60201c565b6200037857600160008084815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055503373ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16837f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45b5050565b60006200038f836200044460201b60201c565b90508160016000858152602001908152602001600020819055508181847fbd79b86ffe0ab8e8776151514217cd7cacd52c909f66475c3af44e129f0b00ff60405160405180910390a4505050565b600080600084815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16905092915050565b600060016000838152602001908152602001600020549050919050565b612b8a80620004716000396000f3fe608060405234801561001057600080fd5b50600436106101e55760003560e01c80638a11d7c91161010f578063d1270485116100a2578063e63ab1e911610071578063e63ab1e91461081b578063fa52c7d814610839578063facd743b14610895578063fe575a87146108f1576101e5565b8063d127048514610713578063d213c0f21461076b578063d37b34d714610789578063d547741f146107cd576101e5565b8063aca2490b116100de578063aca2490b14610639578063c49baebe14610689578063c81b356b146106a7578063cf5ebdf1146106c5576101e5565b80638a11d7c9146104f65780638da5cb5b1461056b57806391d14854146105b5578063a217fddf1461061b576101e5565b806340a141ff116101875780635c975abb116101565780635c975abb1461048e578063697c6045146104b057806375b238fc146104ce5780638456cb59146104ec576101e5565b806340a141ff146103665780634d238c8e146103aa578063537df3b6146103ee578063574b5ff014610432576101e5565b806330a3e2d9116101c357806330a3e2d91461029857806336568abe146102f057806337deea701461033e5780633f4ba83a1461035c576101e5565b80631c9ee11a146101ea578063248a9ca3146102085780632f2ff15d1461024a575b600080fd5b6101f261094d565b6040518082815260200191505060405180910390f35b6102346004803603602081101561021e57600080fd5b8101908080359060200190929190505050610986565b6040518082815260200191505060405180910390f35b6102966004803603604081101561026057600080fd5b8101908080359060200190929190803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506109a3565b005b6102da600480360360208110156102ae57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610b64565b6040518082815260200191505060405180910390f35b61033c6004803603604081101561030657600080fd5b8101908080359060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610b7c565b005b610346610c0e565b6040518082815260200191505060405180910390f35b610364610c18565b005b6103a86004803603602081101561037c57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610d93565b005b6103ec600480360360208110156103c057600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919050505061113e565b005b6104306004803603602081101561040457600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050611618565b005b6104746004803603602081101561044857600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050611850565b604051808215151515815260200191505060405180910390f35b610496611870565b604051808215151515815260200191505060405180910390f35b6104b8611887565b6040518082815260200191505060405180910390f35b6104d661188c565b6040518082815260200191505060405180910390f35b6104f46118c5565b005b6105386004803603602081101561050c57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050611a41565b60405180851515151581526020018415151515815260200183815260200182815260200194505050505060405180910390f35b610573611b70565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b610601600480360360408110156105cb57600080fd5b8101908080359060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050611b9a565b604051808215151515815260200191505060405180910390f35b610623611c01565b6040518082815260200191505060405180910390f35b6106876004803603604081101561064f57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803515159060200190929190505050611c08565b005b610691611fbe565b6040518082815260200191505060405180910390f35b6106af611ff7565b6040518082815260200191505060405180910390f35b610711600480360360408110156106db57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190505050611ffd565b005b6107556004803603602081101561072957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919050505061228a565b6040518082815260200191505060405180910390f35b6107736122a2565b6040518082815260200191505060405180910390f35b6107cb6004803603602081101561079f57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506122a8565b005b610819600480360360408110156107e357600080fd5b8101908080359060200190929190803573ffffffffffffffffffffffffffffffffffffffff16906020019092919050505061255f565b005b61082361267b565b6040518082815260200191505060405180910390f35b61087b6004803603602081101561084f57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506126b4565b604051808215151515815260200191505060405180910390f35b6108d7600480360360208110156108ab57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506126d4565b604051808215151515815260200191505060405180910390f35b6109336004803603602081101561090757600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050612791565b604051808215151515815260200191505060405180910390f35b60405180807f434f4d50414e595f524f4c450000000000000000000000000000000000000000815250600c019050604051809103902081565b600060016000838152602001908152602001600020549050919050565b6109ac82610986565b6109b68133611b9a565b610a0b576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b81600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff161415610aaf576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b82600260008273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1615610b53576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526025815260200180612a5d6025913960400191505060405180910390fd5b610b5d85856127e7565b5050505050565b60086020528060005260406000206000915090505481565b3373ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff1614610c00576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602f815260200180612b02602f913960400191505060405180910390fd5b610c0a82826128bd565b5050565b6000600954905090565b60405180807f5041555345525f524f4c45000000000000000000000000000000000000000000815250600b0190506040518091039020610c588133611b9a565b610cad576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b600360009054906101000a900460ff16610d12576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526025815260200180612b316025913960400191505060405180910390fd5b6000600360006101000a81548160ff0219169083151502179055507f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa33604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390a150565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a0190506040518091039020610dd38133611b9a565b610e28576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b600360009054906101000a900460ff1615610e8e576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260218152602001806129eb6021913960400191505060405180910390fd5b81600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff161415610f32576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b600560008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16610fd4576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602b8152602001806129c0602b913960400191505060405180910390fd5b6000600560008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055506000600660008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff021916908315150217905550600960008154809291906001900391905055506110d660405180807f56414c494441544f525f524f4c45000000000000000000000000000000000000815250600e0190506040518091039020846128bd565b7fe1434e25d6611e0db941968fdc97811c982ac1602e951637d206f5fdda9dd8f183604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390a1505050565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a019050604051809103902061117e8133611b9a565b6111d3576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b600360009054906101000a900460ff1615611239576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260218152602001806129eb6021913960400191505060405180910390fd5b81600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff1614156112dd576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b82600260008273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1615611381576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526025815260200180612a5d6025913960400191505060405180910390fd5b600560008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1615611424576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602b815260200180612a32602b913960400191505060405180910390fd5b6001600560008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055506001600660008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055506101f4600760008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208190555042600860008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055506009600081548092919060010191905055506115af60405180807f56414c494441544f525f524f4c45000000000000000000000000000000000000815250600e0190506040518091039020856127e7565b7fe366c1c0452ed8eec96861e9e54141ebff23c9ec89fe27b996b45f5ec388498784604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390a150505050565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a01905060405180910390206116588133611b9a565b6116ad576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b81600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff161415611751576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b600260008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff166117f3576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526026815260200180612a0c6026913960400191505060405180910390fd5b6000600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff021916908315150217905550505050565b60066020528060005260406000206000915054906101000a900460ff1681565b6000600360009054906101000a900460ff16905090565b600081565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a019050604051809103902081565b60405180807f5041555345525f524f4c45000000000000000000000000000000000000000000815250600b01905060405180910390206119058133611b9a565b61195a576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b600360009054906101000a900460ff16156119c0576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260218152602001806129eb6021913960400191505060405180910390fd5b6001600360006101000a81548160ff0219169083151502179055507f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a25833604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390a150565b600080600080600560008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16600660008773ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16600760008873ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054600860008973ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000205493509350935093509193509193565b6000600360019054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905090565b600080600084815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16905092915050565b6000801b81565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a0190506040518091039020611c488133611b9a565b611c9d576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b600360009054906101000a900460ff1615611d03576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260218152602001806129eb6021913960400191505060405180910390fd5b82600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff161415611da7576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b600560008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff16611e49576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602b8152602001806129c0602b913960400191505060405180910390fd5b821515600660008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1615151415611ef2576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526025815260200180612add6025913960400191505060405180910390fd5b82600660008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055507f805c9c534537bef15c327683ea9e388211b8577686ab415f96941b440980c2d48484604051808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001821515151581526020019250505060405180910390a150505050565b60405180807f56414c494441544f525f524f4c45000000000000000000000000000000000000815250600e019050604051809103902081565b60095481565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a019050604051809103902061203d8133611b9a565b612092576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b82600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff161415612136576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b600560008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff166121d8576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602b8152602001806129c0602b913960400191505060405180910390fd5b600083101580156121eb57506103e88311155b612240576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602b815260200180612995602b913960400191505060405180910390fd5b82600760008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208190555050505050565b60076020528060005260406000206000915090505481565b6103e881565b60405180807f41444d494e5f524f4c4500000000000000000000000000000000000000000000815250600a01905060405180910390206122e88133611b9a565b61233d576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b81600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff1614156123e1576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b600260008473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1615612484576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602a815260200180612a82602a913960400191505060405180910390fd5b6001600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff02191690831515021790555061251b60405180807f56414c494441544f525f524f4c45000000000000000000000000000000000000815250600e0190506040518091039020846128bd565b61255a60405180807f434f4d50414e595f524f4c450000000000000000000000000000000000000000815250600c0190506040518091039020846128bd565b505050565b61256882610986565b6125728133611b9a565b6125c7576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401808060200182810382526031815260200180612aac6031913960400191505060405180910390fd5b81600073ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16141561266b576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f416363657373436f6e74726f6c3a20696e76616c69642061646472657373000081525060200191505060405180910390fd5b61267584846128bd565b50505050565b60405180807f5041555345525f524f4c45000000000000000000000000000000000000000000815250600b019050604051809103902081565b60056020528060005260406000206000915054906101000a900460ff1681565b6000600560008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff1680156127785750600660008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff165b801561278a575061278882612791565b155b9050919050565b6000600260008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060009054906101000a900460ff169050919050565b6127f18282611b9a565b6128b957600160008084815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055503373ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16837f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45b5050565b6128c78282611b9a565b1561299057600080600084815260200190815260200160002060008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060006101000a81548160ff0219169083151502179055503373ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16837ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b60405160405180910390a45b505056fe56616c696461746f7252656769737472793a2072657075746174696f6e206f7574206f6620626f756e647356616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374416363657373436f6e74726f6c3a20636f6e747261637420697320706175736564416363657373436f6e74726f6c3a206163636f756e74206e6f7420626c61636b6c697374656456616c696461746f7252656769737472793a2076616c696461746f7220616c726561647920657869737473416363657373436f6e74726f6c3a206163636f756e7420697320626c61636b6c6973746564416363657373436f6e74726f6c3a206163636f756e7420616c726561647920626c61636b6c6973746564416363657373436f6e74726f6c3a2063616c6c657220646f6573206e6f74206861766520726571756972656420726f6c6556616c696461746f7252656769737472793a2073746174757320616c726561647920736574416363657373436f6e74726f6c3a2063616e206f6e6c792072656e6f756e636520726f6c657320666f722073656c66416363657373436f6e74726f6c3a20636f6e7472616374206973206e6f7420706175736564a265627a7a723158209fb16c53d736ed46db2abe046ce01748443654198eb6665c845a93303ccdc1f564736f6c63430005110032", "deployedBytecode": "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", "sourceMap": "184:4717:6:-;;;1573:10:0;1564:6;;:19;;;;;;;;;;;;;;;;;;1603:5;1593:7;;:15;;;;;;;;;;;;;;;;;;1403:1;1618:17;:32;;;;1717:42;745:4;1728:18;;1748:10;1717;;;:42;;:::i;:::-;1769:34;792:23;;;;;;;;;;;;;;;;;;;1792:10;1769;;;:34;;:::i;:::-;1813:35;1003:24;;;;;;;;;;;;;;;;;;;1837:10;1813;;;:35;;:::i;:::-;1894:45;792:23;;;;;;;;;;;;;;;;;;;745:4;1920:18;;1894:13;;;:45;;:::i;:::-;1949:41;862:27;;;;;;;;;;;;;;;;;;;792:23;;;;;;;;;;;;;;;;;;;1949:13;;;:41;;:::i;:::-;2000:39;934:25;;;;;;;;;;;;;;;;;;;792:23;;;;;;;;;;;;;;;;;;;2000:13;;;:39;;:::i;:::-;2049:46;1003:24;;;;;;;;;;;;;;;;;;;745:4;2076:18;;2049:13;;;:46;;:::i;:::-;184:4717:6;;6460:215:0;6535:22;6543:4;6549:7;6535;;;:22;;:::i;:::-;6530:139;;6597:4;6573:6;:12;6580:4;6573:12;;;;;;;;;;;:21;6586:7;6573:21;;;;;;;;;;;;;;;;:28;;;;;;;;;;;;;;;;;;6647:10;6620:38;;6638:7;6620:38;;6632:4;6620:38;;;;;;;;;;6530:139;6460:215;;:::o;7028:234::-;7103:25;7131:18;7144:4;7131:12;;;:18;;:::i;:::-;7103:46;;7179:9;7159:11;:17;7171:4;7159:17;;;;;;;;;;;:29;;;;7245:9;7226:17;7220:4;7203:52;;;;;;;;;;7028:234;;;:::o;3687:120::-;3756:4;3779:6;:12;3786:4;3779:12;;;;;;;;;;;:21;3792:7;3779:21;;;;;;;;;;;;;;;;;;;;;;;;;3772:28;;3687:120;;;;:::o;3881:107::-;3938:7;3964:11;:17;3976:4;3964:17;;;;;;;;;;;;3957:24;;3881:107;;;:::o;184:4717:6:-;;;;;;;", "deployedSourceMap": "184:4717:6:-;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;184:4717:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;895:64:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;3881:107;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;3881:107:0;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;4049:213;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;4049:213:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;591:52:6;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;591:52:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;4593:194:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;4593:194:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;4795:104:6;;;:::i;:::-;;;;;;;;;;;;;;;;;;;5036:126:0;;;:::i;:::-;;1786:495:6;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1786:495:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;949:695;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;949:695:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;5831:255:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;5831:255:0;;;;;;;;;;;;;;;;;;;:::i;:::-;;478:47:6;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;478:47:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;5224:76:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;686:42:6;;;:::i;:::-;;;;;;;;;;;;;;;;;;;755:60:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;4857:124;;;:::i;:::-;;4297:372:6;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;4297:372:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6317:77:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;3687:120;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;3687:120:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;700:49;;;:::i;:::-;;;;;;;;;;;;;;;;;;;2453:518:6;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;2453:518:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;821:68:0;;;:::i;:::-;;;;;;;;;;;;;;;;;;;650:30:6;;;:::i;:::-;;;;;;;;;;;;;;;;;;;3180:526;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;3180:526:6;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;531:54;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;531:54:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;734:45;;;:::i;:::-;;;;;;;;;;;;;;;;;;;5359:408:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;5359:408:0;;;;;;;;;;;;;;;;;;;:::i;:::-;;4326:183;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;4326:183:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;965:62;;;:::i;:::-;;;;;;;;;;;;;;;;;;;430:42:6;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;430:42:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;3917:211;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;3917:211:6;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;6152:112:0;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;6152:112:0;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;895:64;934:25;;;;;;;;;;;;;;;;;;;895:64;:::o;3881:107::-;3938:7;3964:11;:17;3976:4;3964:17;;;;;;;;;;;;3957:24;;3881:107;;;:::o;4049:213::-;4133:18;4146:4;4133:12;:18::i;:::-;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4175:7;3342:1;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4207:7;2839:12;:21;2852:7;2839:21;;;;;;;;;;;;;;;;;;;;;;;;;2838:22;2830:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4230:25;4241:4;4247:7;4230:10;:25::i;:::-;3389:1;2323;4049:213;;;:::o;591:52:6:-;;;;;;;;;;;;;;;;;:::o;4593:194:0:-;4682:10;4671:21;;:7;:21;;;4663:81;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4754:26;4766:4;4772:7;4754:11;:26::i;:::-;4593:194;;:::o;4795:104:6:-;4851:7;4877:15;;4870:22;;4795:104;:::o;5036:126:0:-;1003:24;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2630:7;;;;;;;;;;;2622:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5115:5;5105:7;;:15;;;;;;;;;;;;;;;;;;5135:20;5144:10;5135:20;;;;;;;;;;;;;;;;;;;;;;5036:126;:::o;1786:495:6:-;792:23:0;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2453:7;;;;;;;;;;;2452:8;2444:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1900:10:6;3342:1:0;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1943:10:6;:22;1954:10;1943:22;;;;;;;;;;;;;;;;;;;;;;;;;1922:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2070:5;2045:10;:22;2056:10;2045:22;;;;;;;;;;;;;;;;:30;;;;;;;;;;;;;;;;;;2115:5;2085:15;:27;2101:10;2085:27;;;;;;;;;;;;;;;;:35;;;;;;;;;;;;;;;;;;2130:15;;:17;;;;;;;;;;;;;;2191:39;862:27:0;;;;;;;;;;;;;;;;;;;2219:10:6;2191:11;:39::i;:::-;2246:28;2263:10;2246:28;;;;;;;;;;;;;;;;;;;;;;2508:1:0;1786:495:6;;:::o;949:695::-;792:23:0;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2453:7;;;;;;;;;;;2452:8;2444:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1092:10:6;3342:1:0;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1127:10:6;2839:12:0;:21;2852:7;2839:21;;;;;;;;;;;;;;;;;;;;;;;;;2838:22;2830:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1175:10:6;:22;1186:10;1175:22;;;;;;;;;;;;;;;;;;;;;;;;;1174:23;1153:113;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1302:4;1277:10;:22;1288:10;1277:22;;;;;;;;;;;;;;;;:29;;;;;;;;;;;;;;;;;;1346:4;1316:15;:27;1332:10;1316:27;;;;;;;;;;;;;;;;:34;;;;;;;;;;;;;;;;;;1394:3;1360:19;:31;1380:10;1360:31;;;;;;;;;;;;;;;:37;;;;1472:15;1440:17;:29;1458:10;1440:29;;;;;;;;;;;;;;;:47;;;;1497:15;;:17;;;;;;;;;;;;;1557:38;862:27:0;;;;;;;;;;;;;;;;;;;1584:10:6;1557;:38::i;:::-;1611:26;1626:10;1611:26;;;;;;;;;;;;;;;;;;;;;;3389:1:0;2508;949:695:6;;:::o;5831:255:0:-;792:23;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5945:7;3342:1;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5976:12;:21;5989:7;5976:21;;;;;;;;;;;;;;;;;;;;;;;;;5968:72;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6074:5;6050:12;:21;6063:7;6050:21;;;;;;;;;;;;;;;;:29;;;;;;;;;;;;;;;;;;2323:1;5831:255;;:::o;478:47:6:-;;;;;;;;;;;;;;;;;;;;;;:::o;5224:76:0:-;5263:4;5286:7;;;;;;;;;;;5279:14;;5224:76;:::o;686:42:6:-;727:1;686:42;:::o;755:60:0:-;792:23;;;;;;;;;;;;;;;;;;;755:60;:::o;4857:124::-;1003:24;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2453:7;;;;;;;;;;;2452:8;2444:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4937:4;4927:7;;:14;;;;;;;;;;;;;;;;;;4956:18;4963:10;4956:18;;;;;;;;;;;;;;;;;;;;;;4857:124;:::o;4297:372:6:-;4402:11;4415;4428:18;4448:16;4501:10;:22;4512:10;4501:22;;;;;;;;;;;;;;;;;;;;;;;;;4537:15;:27;4553:10;4537:27;;;;;;;;;;;;;;;;;;;;;;;;;4578:19;:31;4598:10;4578:31;;;;;;;;;;;;;;;;4623:17;:29;4641:10;4623:29;;;;;;;;;;;;;;;;4480:182;;;;;;;;4297:372;;;;;:::o;6317:77:0:-;6355:7;6381:6;;;;;;;;;;;6374:13;;6317:77;:::o;3687:120::-;3756:4;3779:6;:12;3786:4;3779:12;;;;;;;;;;;:21;3792:7;3779:21;;;;;;;;;;;;;;;;;;;;;;;;;3772:28;;3687:120;;;;:::o;700:49::-;745:4;700:49;;;:::o;2453:518:6:-;792:23:0;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2453:7;;;;;;;;;;;2452:8;2444:54;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2592:10:6;3342:1:0;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2635:10:6;:22;2646:10;2635:22;;;;;;;;;;;;;;;;;;;;;;;;;2614:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2788:7;2757:38;;:15;:27;2773:10;2757:27;;;;;;;;;;;;;;;;;;;;;;;;;:38;;;;2736:122;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2899:7;2869:15;:27;2885:10;2869:27;;;;;;;;;;;;;;;;:37;;;;;;;;;;;;;;;;;;2921:43;2944:10;2956:7;2921:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2508:1:0;2453:518:6;;;:::o;821:68:0:-;862:27;;;;;;;;;;;;;;;;;;;821:68;:::o;650:30:6:-;;;;:::o;3180:526::-;792:23:0;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3322:10:6;3342:1:0;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3365:10:6;:22;3376:10;3365:22;;;;;;;;;;;;;;;;;;;;;;;;;3344:112;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;727:1;3487:14;:32;;:84;;;;;775:4;3539:14;:32;;3487:84;3466:174;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3685:14;3651:19;:31;3671:10;3651:31;;;;;;;;;;;;;;;:48;;;;2323:1:0;3180:526:6;;;:::o;531:54::-;;;;;;;;;;;;;;;;;:::o;734:45::-;775:4;734:45;:::o;5359:408:0:-;792:23;;;;;;;;;;;;;;;;;;;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5470:7;3342:1;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5502:12;:21;5515:7;5502:21;;;;;;;;;;;;;;;;;;;;;;;;;5501:22;5493:77;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;5604:4;5580:12;:21;5593:7;5580:21;;;;;;;;;;;;;;;;:28;;;;;;;;;;;;;;;;;;5680:36;862:27;;;;;;;;;;;;;;;;;;;5708:7;5680:11;:36::i;:::-;5726:34;934:25;;;;;;;;;;;;;;;;;;;5752:7;5726:11;:34::i;:::-;2323:1;5359:408;;:::o;4326:183::-;4411:18;4424:4;4411:12;:18::i;:::-;2234:25;2242:4;2248:10;2234:7;:25::i;:::-;2226:87;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4453:7;3342:1;3323:21;;:7;:21;;;;3315:64;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4476:26;4488:4;4494:7;4476:11;:26::i;:::-;2323:1;4326:183;;;:::o;965:62::-;1003:24;;;;;;;;;;;;;;;;;;;965:62;:::o;430:42:6:-;;;;;;;;;;;;;;;;;;;;;;:::o;3917:211::-;3979:4;4014:10;:22;4025:10;4014:22;;;;;;;;;;;;;;;;;;;;;;;;;:65;;;;;4052:15;:27;4068:10;4052:27;;;;;;;;;;;;;;;;;;;;;;;;;4014:65;:107;;;;;4096:25;4110:10;4096:13;:25::i;:::-;4095:26;4014:107;3995:126;;3917:211;;;:::o;6152:112:0:-;6213:4;6236:12;:21;6249:7;6236:21;;;;;;;;;;;;;;;;;;;;;;;;;6229:28;;6152:112;;;:::o;6460:215::-;6535:22;6543:4;6549:7;6535;:22::i;:::-;6530:139;;6597:4;6573:6;:12;6580:4;6573:12;;;;;;;;;;;:21;6586:7;6573:21;;;;;;;;;;;;;;;;:28;;;;;;;;;;;;;;;;;;6647:10;6620:38;;6638:7;6620:38;;6632:4;6620:38;;;;;;;;;;6530:139;6460:215;;:::o;6742:216::-;6817:22;6825:4;6831:7;6817;:22::i;:::-;6813:139;;;6879:5;6855:6;:12;6862:4;6855:12;;;;;;;;;;;:21;6868:7;6855:21;;;;;;;;;;;;;;;;:29;;;;;;;;;;;;;;;;;;6930:10;6903:38;;6921:7;6903:38;;6915:4;6903:38;;;;;;;;;;6813:139;6742:216;;:::o", "source": "pragma solidity ^0.5.0;\n\nimport \"./AccessControl.sol\";\n\n/**\n * @title ValidatorRegistry\n * @dev A contract for managing a registry of validators with comprehensive access control.\n */\ncontract ValidatorRegistry is AccessControl {\n    // Events\n    event ValidatorAdded(address validator);\n    event ValidatorRemoved(address validator);\n    event ValidatorStatusChanged(address validator, bool active);\n\n    // State variables\n    mapping(address => bool) public validators;\n    mapping(address => bool) public validatorActive;\n    mapping(address => uint256) public validatorReputation;\n    mapping(address => uint256) public validatorJoinDate;\n\n    uint256 public totalValidators;\n    uint256 public constant MIN_REPUTATION = 0;\n    uint256 public constant MAX_REPUTATION = 1000;\n\n    /**\n     * @dev Adds a new validator to the registry with comprehensive validation.\n     * @param _validator The address of the validator to be added.\n     */\n    function addValidator(\n        address _validator\n    )\n        public\n        onlyRole(ADMIN_ROLE)\n        whenNotPaused\n        validAddress(_validator)\n        notBlacklisted(_validator)\n    {\n        require(\n            !validators[_validator],\n            \"ValidatorRegistry: validator already exists\"\n        );\n\n        validators[_validator] = true;\n        validatorActive[_validator] = true;\n        validatorReputation[_validator] = 500; // Start with neutral reputation\n        validatorJoinDate[_validator] = block.timestamp;\n        totalValidators++;\n\n        // Grant validator role\n        _grantRole(VALIDATOR_ROLE, _validator);\n\n        emit ValidatorAdded(_validator);\n    }\n\n    /**\n     * @dev Removes a validator from the registry.\n     * @param _validator The address of the validator to be removed.\n     */\n    function removeValidator(\n        address _validator\n    ) public onlyRole(ADMIN_ROLE) whenNotPaused validAddress(_validator) {\n        require(\n            validators[_validator],\n            \"ValidatorRegistry: validator does not exist\"\n        );\n\n        validators[_validator] = false;\n        validatorActive[_validator] = false;\n        totalValidators--;\n\n        // Revoke validator role\n        _revokeRole(VALIDATOR_ROLE, _validator);\n\n        emit ValidatorRemoved(_validator);\n    }\n\n    /**\n     * @dev Activates or deactivates a validator.\n     * @param _validator The address of the validator.\n     * @param _active The new active status.\n     */\n    function setValidatorStatus(\n        address _validator,\n        bool _active\n    ) public onlyRole(ADMIN_ROLE) whenNotPaused validAddress(_validator) {\n        require(\n            validators[_validator],\n            \"ValidatorRegistry: validator does not exist\"\n        );\n        require(\n            validatorActive[_validator] != _active,\n            \"ValidatorRegistry: status already set\"\n        );\n\n        validatorActive[_validator] = _active;\n        emit ValidatorStatusChanged(_validator, _active);\n    }\n\n    /**\n     * @dev Updates validator reputation (can be called by market contract).\n     * @param _validator The address of the validator.\n     * @param _newReputation The new reputation score.\n     */\n    function updateValidatorReputation(\n        address _validator,\n        uint256 _newReputation\n    ) public onlyRole(ADMIN_ROLE) validAddress(_validator) {\n        require(\n            validators[_validator],\n            \"ValidatorRegistry: validator does not exist\"\n        );\n        require(\n            _newReputation >= MIN_REPUTATION &&\n                _newReputation <= MAX_REPUTATION,\n            \"ValidatorRegistry: reputation out of bounds\"\n        );\n\n        validatorReputation[_validator] = _newReputation;\n    }\n\n    /**\n     * @dev Checks if an address is an active validator.\n     * @param _validator The address to be checked.\n     * @return A boolean indicating whether the address is an active validator.\n     */\n    function isValidator(address _validator) public view returns (bool) {\n        return\n            validators[_validator] &&\n            validatorActive[_validator] &&\n            !isBlacklisted(_validator);\n    }\n\n    /**\n     * @dev Gets validator information.\n     * @param _validator The address of the validator.\n     * @return exists, active, reputation, joinDate\n     */\n    function getValidatorInfo(\n        address _validator\n    )\n        public\n        view\n        returns (bool exists, bool active, uint256 reputation, uint256 joinDate)\n    {\n        return (\n            validators[_validator],\n            validatorActive[_validator],\n            validatorReputation[_validator],\n            validatorJoinDate[_validator]\n        );\n    }\n\n    /**\n     * @dev Gets the total number of active validators.\n     * @return The number of active validators.\n     */\n    function getActiveValidatorCount() public view returns (uint256) {\n        return totalValidators;\n    }\n}\n", "sourcePath": "C:\\Users\\<USER>\\Downloads\\EcoXChange\\contracts\\ValidatorRegistry.sol", "ast": {"absolutePath": "project:/contracts/ValidatorRegistry.sol", "exportedSymbols": {"ValidatorRegistry": [4931]}, "id": 4932, "nodeType": "SourceUnit", "nodes": [{"id": 4644, "literals": ["solidity", "^", "0.5", ".0"], "nodeType": "PragmaDirective", "src": "0:23:6"}, {"absolutePath": "project:/contracts/AccessControl.sol", "file": "./AccessControl.sol", "id": 4645, "nodeType": "ImportDirective", "scope": 4932, "sourceUnit": 538, "src": "25:29:6", "symbolAliases": [], "unitAlias": ""}, {"baseContracts": [{"arguments": null, "baseName": {"contractScope": null, "id": 4646, "name": "AccessControl", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 537, "src": "214:13:6", "typeDescriptions": {"typeIdentifier": "t_contract$_AccessControl_$537", "typeString": "contract AccessControl"}}, "id": 4647, "nodeType": "InheritanceSpecifier", "src": "214:13:6"}], "contractDependencies": [537], "contractKind": "contract", "documentation": "@title ValidatorRegistry\n@dev A contract for managing a registry of validators with comprehensive access control.", "fullyImplemented": true, "id": 4931, "linearizedBaseContracts": [4931, 537], "name": "ValidatorRegistry", "nodeType": "ContractDefinition", "nodes": [{"anonymous": false, "documentation": null, "id": 4651, "name": "ValidatorAdded", "nodeType": "EventDefinition", "parameters": {"id": 4650, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4649, "indexed": false, "name": "validator", "nodeType": "VariableDeclaration", "scope": 4651, "src": "269:17:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4648, "name": "address", "nodeType": "ElementaryTypeName", "src": "269:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "268:19:6"}, "src": "248:40:6"}, {"anonymous": false, "documentation": null, "id": 4655, "name": "ValidatorRemoved", "nodeType": "EventDefinition", "parameters": {"id": 4654, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4653, "indexed": false, "name": "validator", "nodeType": "VariableDeclaration", "scope": 4655, "src": "316:17:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4652, "name": "address", "nodeType": "ElementaryTypeName", "src": "316:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "315:19:6"}, "src": "293:42:6"}, {"anonymous": false, "documentation": null, "id": 4661, "name": "ValidatorStatusChanged", "nodeType": "EventDefinition", "parameters": {"id": 4660, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4657, "indexed": false, "name": "validator", "nodeType": "VariableDeclaration", "scope": 4661, "src": "369:17:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4656, "name": "address", "nodeType": "ElementaryTypeName", "src": "369:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4659, "indexed": false, "name": "active", "nodeType": "VariableDeclaration", "scope": 4661, "src": "388:11:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 4658, "name": "bool", "nodeType": "ElementaryTypeName", "src": "388:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "368:32:6"}, "src": "340:61:6"}, {"constant": false, "id": 4665, "name": "validators", "nodeType": "VariableDeclaration", "scope": 4931, "src": "430:42:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 4664, "keyType": {"id": 4662, "name": "address", "nodeType": "ElementaryTypeName", "src": "438:7:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "430:24:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueType": {"id": 4663, "name": "bool", "nodeType": "ElementaryTypeName", "src": "449:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "value": null, "visibility": "public"}, {"constant": false, "id": 4669, "name": "validatorActive", "nodeType": "VariableDeclaration", "scope": 4931, "src": "478:47:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 4668, "keyType": {"id": 4666, "name": "address", "nodeType": "ElementaryTypeName", "src": "486:7:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "478:24:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueType": {"id": 4667, "name": "bool", "nodeType": "ElementaryTypeName", "src": "497:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "value": null, "visibility": "public"}, {"constant": false, "id": 4673, "name": "validatorReputation", "nodeType": "VariableDeclaration", "scope": 4931, "src": "531:54:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 4672, "keyType": {"id": 4670, "name": "address", "nodeType": "ElementaryTypeName", "src": "539:7:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "531:27:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 4671, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "550:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "public"}, {"constant": false, "id": 4677, "name": "validatorJoinDate", "nodeType": "VariableDeclaration", "scope": 4931, "src": "591:52:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 4676, "keyType": {"id": 4674, "name": "address", "nodeType": "ElementaryTypeName", "src": "599:7:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "591:27:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 4675, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "610:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "public"}, {"constant": false, "id": 4679, "name": "totalValidators", "nodeType": "VariableDeclaration", "scope": 4931, "src": "650:30:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4678, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "650:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "public"}, {"constant": true, "id": 4682, "name": "MIN_REPUTATION", "nodeType": "VariableDeclaration", "scope": 4931, "src": "686:42:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4680, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "686:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"argumentTypes": null, "hexValue": "30", "id": 4681, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "727:1:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "visibility": "public"}, {"constant": true, "id": 4685, "name": "MAX_REPUTATION", "nodeType": "VariableDeclaration", "scope": 4931, "src": "734:45:6", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4683, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "734:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"argumentTypes": null, "hexValue": "31303030", "id": 4684, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "775:4:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_1000_by_1", "typeString": "int_const 1000"}, "value": "1000"}, "visibility": "public"}, {"body": {"id": 4746, "nodeType": "Block", "src": "1143:501:6", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4705, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1174:23:6", "subExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4702, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "1175:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4704, "indexExpression": {"argumentTypes": null, "id": 4703, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1186:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1175:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "56616c696461746f7252656769737472793a2076616c696461746f7220616c726561647920657869737473", "id": 4706, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1211:45:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_7100ff56886f5a774be10aedbf1775a78c96801eb279aad6879fc05a23d84f4b", "typeString": "literal_string \"ValidatorRegistry: validator already exists\""}, "value": "ValidatorRegistry: validator already exists"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_7100ff56886f5a774be10aedbf1775a78c96801eb279aad6879fc05a23d84f4b", "typeString": "literal_string \"ValidatorRegistry: validator already exists\""}], "id": 4701, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "1153:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4707, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1153:113:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4708, "nodeType": "ExpressionStatement", "src": "1153:113:6"}, {"expression": {"argumentTypes": null, "id": 4713, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4709, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "1277:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4711, "indexExpression": {"argumentTypes": null, "id": 4710, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1288:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1277:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "74727565", "id": 4712, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1302:4:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "1277:29:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4714, "nodeType": "ExpressionStatement", "src": "1277:29:6"}, {"expression": {"argumentTypes": null, "id": 4719, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4715, "name": "validatorActive", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4669, "src": "1316:15:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4717, "indexExpression": {"argumentTypes": null, "id": 4716, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1332:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1316:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "74727565", "id": 4718, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1346:4:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "1316:34:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4720, "nodeType": "ExpressionStatement", "src": "1316:34:6"}, {"expression": {"argumentTypes": null, "id": 4725, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4721, "name": "validatorReputation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4673, "src": "1360:19:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 4723, "indexExpression": {"argumentTypes": null, "id": 4722, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1380:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1360:31:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "353030", "id": 4724, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1394:3:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_500_by_1", "typeString": "int_const 500"}, "value": "500"}, "src": "1360:37:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 4726, "nodeType": "ExpressionStatement", "src": "1360:37:6"}, {"expression": {"argumentTypes": null, "id": 4732, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4727, "name": "validatorJoinDate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4677, "src": "1440:17:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 4729, "indexExpression": {"argumentTypes": null, "id": 4728, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1458:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1440:29:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 4730, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4936, "src": "1472:5:6", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 4731, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "timestamp", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1472:15:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1440:47:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 4733, "nodeType": "ExpressionStatement", "src": "1440:47:6"}, {"expression": {"argumentTypes": null, "id": 4735, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1497:17:6", "subExpression": {"argumentTypes": null, "id": 4734, "name": "totalValidators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4679, "src": "1497:15:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 4736, "nodeType": "ExpressionStatement", "src": "1497:17:6"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4738, "name": "VALIDATOR_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 46, "src": "1568:14:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"argumentTypes": null, "id": 4739, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1584:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 4737, "name": "_grantRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 481, "src": "1557:10:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$__$", "typeString": "function (bytes32,address)"}}, "id": 4740, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1557:38:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4741, "nodeType": "ExpressionStatement", "src": "1557:38:6"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4743, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1626:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 4742, "name": "ValidatorAdded", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4651, "src": "1611:14:6", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 4744, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1611:26:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4745, "nodeType": "EmitStatement", "src": "1606:31:6"}]}, "documentation": "@dev Adds a new validator to the registry with comprehensive validation.\n@param _validator The address of the validator to be added.", "id": 4747, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"argumentTypes": null, "id": 4690, "name": "ADMIN_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41, "src": "1037:10:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 4691, "modifierName": {"argumentTypes": null, "id": 4689, "name": "only<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "1028:8:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_bytes32_$", "typeString": "modifier (bytes32)"}}, "nodeType": "ModifierInvocation", "src": "1028:20:6"}, {"arguments": null, "id": 4693, "modifierName": {"argumentTypes": null, "id": 4692, "name": "whenNotPaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 162, "src": "1057:13:6", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "1057:13:6"}, {"arguments": [{"argumentTypes": null, "id": 4695, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1092:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 4696, "modifierName": {"argumentTypes": null, "id": 4694, "name": "validAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "1079:12:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_address_$", "typeString": "modifier (address)"}}, "nodeType": "ModifierInvocation", "src": "1079:24:6"}, {"arguments": [{"argumentTypes": null, "id": 4698, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4687, "src": "1127:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 4699, "modifierName": {"argumentTypes": null, "id": 4697, "name": "notBlacklisted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 185, "src": "1112:14:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_address_$", "typeString": "modifier (address)"}}, "nodeType": "ModifierInvocation", "src": "1112:26:6"}], "name": "addValidator", "nodeType": "FunctionDefinition", "parameters": {"id": 4688, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4687, "name": "_validator", "nodeType": "VariableDeclaration", "scope": 4747, "src": "980:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4686, "name": "address", "nodeType": "ElementaryTypeName", "src": "980:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "970:34:6"}, "returnParameters": {"id": 4700, "nodeType": "ParameterList", "parameters": [], "src": "1143:0:6"}, "scope": 4931, "src": "949:695:6", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4791, "nodeType": "Block", "src": "1912:369:6", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4761, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "1943:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4763, "indexExpression": {"argumentTypes": null, "id": 4762, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4749, "src": "1954:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1943:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "56616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374", "id": 4764, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1979:45:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}, "value": "ValidatorRegistry: validator does not exist"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}], "id": 4760, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "1922:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4765, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1922:112:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4766, "nodeType": "ExpressionStatement", "src": "1922:112:6"}, {"expression": {"argumentTypes": null, "id": 4771, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4767, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "2045:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4769, "indexExpression": {"argumentTypes": null, "id": 4768, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4749, "src": "2056:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2045:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "66616c7365", "id": 4770, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2070:5:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "2045:30:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4772, "nodeType": "ExpressionStatement", "src": "2045:30:6"}, {"expression": {"argumentTypes": null, "id": 4777, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4773, "name": "validatorActive", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4669, "src": "2085:15:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4775, "indexExpression": {"argumentTypes": null, "id": 4774, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4749, "src": "2101:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2085:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "66616c7365", "id": 4776, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2115:5:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "2085:35:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4778, "nodeType": "ExpressionStatement", "src": "2085:35:6"}, {"expression": {"argumentTypes": null, "id": 4780, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "--", "prefix": false, "src": "2130:17:6", "subExpression": {"argumentTypes": null, "id": 4779, "name": "totalValidators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4679, "src": "2130:15:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 4781, "nodeType": "ExpressionStatement", "src": "2130:17:6"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4783, "name": "VALIDATOR_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 46, "src": "2203:14:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"argumentTypes": null, "id": 4784, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4749, "src": "2219:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 4782, "name": "_revokeRole", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 510, "src": "2191:11:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_bytes32_$_t_address_$returns$__$", "typeString": "function (bytes32,address)"}}, "id": 4785, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2191:39:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4786, "nodeType": "ExpressionStatement", "src": "2191:39:6"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4788, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4749, "src": "2263:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 4787, "name": "ValidatorRemoved", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4655, "src": "2246:16:6", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 4789, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2246:28:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4790, "nodeType": "EmitStatement", "src": "2241:33:6"}]}, "documentation": "@dev Removes a validator from the registry.\n@param _validator The address of the validator to be removed.", "id": 4792, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"argumentTypes": null, "id": 4752, "name": "ADMIN_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41, "src": "1861:10:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 4753, "modifierName": {"argumentTypes": null, "id": 4751, "name": "only<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "1852:8:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_bytes32_$", "typeString": "modifier (bytes32)"}}, "nodeType": "ModifierInvocation", "src": "1852:20:6"}, {"arguments": null, "id": 4755, "modifierName": {"argumentTypes": null, "id": 4754, "name": "whenNotPaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 162, "src": "1873:13:6", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "1873:13:6"}, {"arguments": [{"argumentTypes": null, "id": 4757, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4749, "src": "1900:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 4758, "modifierName": {"argumentTypes": null, "id": 4756, "name": "validAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "1887:12:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_address_$", "typeString": "modifier (address)"}}, "nodeType": "ModifierInvocation", "src": "1887:24:6"}], "name": "removeValidator", "nodeType": "FunctionDefinition", "parameters": {"id": 4750, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4749, "name": "_validator", "nodeType": "VariableDeclaration", "scope": 4792, "src": "1820:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4748, "name": "address", "nodeType": "ElementaryTypeName", "src": "1820:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "1810:34:6"}, "returnParameters": {"id": 4759, "nodeType": "ParameterList", "parameters": [], "src": "1912:0:6"}, "scope": 4931, "src": "1786:495:6", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4834, "nodeType": "Block", "src": "2604:367:6", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4808, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "2635:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4810, "indexExpression": {"argumentTypes": null, "id": 4809, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4794, "src": "2646:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2635:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "56616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374", "id": 4811, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2671:45:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}, "value": "ValidatorRegistry: validator does not exist"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}], "id": 4807, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "2614:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4812, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2614:112:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4813, "nodeType": "ExpressionStatement", "src": "2614:112:6"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 4819, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4815, "name": "validatorActive", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4669, "src": "2757:15:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4817, "indexExpression": {"argumentTypes": null, "id": 4816, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4794, "src": "2773:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2757:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"argumentTypes": null, "id": 4818, "name": "_active", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4796, "src": "2788:7:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2757:38:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "56616c696461746f7252656769737472793a2073746174757320616c726561647920736574", "id": 4820, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2809:39:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_da30e9c7e95ccbe8d139701698b417bd0b410f829de465171ce2c4b9af9c7560", "typeString": "literal_string \"ValidatorRegistry: status already set\""}, "value": "ValidatorRegistry: status already set"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_da30e9c7e95ccbe8d139701698b417bd0b410f829de465171ce2c4b9af9c7560", "typeString": "literal_string \"ValidatorRegistry: status already set\""}], "id": 4814, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "2736:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4821, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2736:122:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4822, "nodeType": "ExpressionStatement", "src": "2736:122:6"}, {"expression": {"argumentTypes": null, "id": 4827, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4823, "name": "validatorActive", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4669, "src": "2869:15:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4825, "indexExpression": {"argumentTypes": null, "id": 4824, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4794, "src": "2885:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2869:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 4826, "name": "_active", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4796, "src": "2899:7:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2869:37:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4828, "nodeType": "ExpressionStatement", "src": "2869:37:6"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4830, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4794, "src": "2944:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4831, "name": "_active", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4796, "src": "2956:7:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 4829, "name": "ValidatorStatusChanged", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4661, "src": "2921:22:6", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_bool_$returns$__$", "typeString": "function (address,bool)"}}, "id": 4832, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2921:43:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4833, "nodeType": "EmitStatement", "src": "2916:48:6"}]}, "documentation": "@dev Activates or deactivates a validator.\n@param _validator The address of the validator.\n@param _active The new active status.", "id": 4835, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"argumentTypes": null, "id": 4799, "name": "ADMIN_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41, "src": "2553:10:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 4800, "modifierName": {"argumentTypes": null, "id": 4798, "name": "only<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "2544:8:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_bytes32_$", "typeString": "modifier (bytes32)"}}, "nodeType": "ModifierInvocation", "src": "2544:20:6"}, {"arguments": null, "id": 4802, "modifierName": {"argumentTypes": null, "id": 4801, "name": "whenNotPaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 162, "src": "2565:13:6", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "2565:13:6"}, {"arguments": [{"argumentTypes": null, "id": 4804, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4794, "src": "2592:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 4805, "modifierName": {"argumentTypes": null, "id": 4803, "name": "validAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "2579:12:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_address_$", "typeString": "modifier (address)"}}, "nodeType": "ModifierInvocation", "src": "2579:24:6"}], "name": "setValidatorStatus", "nodeType": "FunctionDefinition", "parameters": {"id": 4797, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4794, "name": "_validator", "nodeType": "VariableDeclaration", "scope": 4835, "src": "2490:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4793, "name": "address", "nodeType": "ElementaryTypeName", "src": "2490:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4796, "name": "_active", "nodeType": "VariableDeclaration", "scope": 4835, "src": "2518:12:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 4795, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2518:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "2480:56:6"}, "returnParameters": {"id": 4806, "nodeType": "ParameterList", "parameters": [], "src": "2604:0:6"}, "scope": 4931, "src": "2453:518:6", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4872, "nodeType": "Block", "src": "3334:372:6", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4849, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "3365:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4851, "indexExpression": {"argumentTypes": null, "id": 4850, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4837, "src": "3376:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3365:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "56616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374", "id": 4852, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3401:45:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}, "value": "ValidatorRegistry: validator does not exist"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}], "id": 4848, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "3344:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4853, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3344:112:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4854, "nodeType": "ExpressionStatement", "src": "3344:112:6"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 4862, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 4858, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 4856, "name": "_newReputation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4839, "src": "3487:14:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 4857, "name": "MIN_REPUTATION", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4682, "src": "3505:14:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3487:32:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 4861, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 4859, "name": "_newReputation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4839, "src": "3539:14:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "id": 4860, "name": "MAX_REPUTATION", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4685, "src": "3557:14:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3539:32:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "3487:84:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "56616c696461746f7252656769737472793a2072657075746174696f6e206f7574206f6620626f756e6473", "id": 4863, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "3585:45:6", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_3803c7dd283bafb3c4aa695244f3032e808192a3d4648033a376423cb7e5e604", "typeString": "literal_string \"ValidatorRegistry: reputation out of bounds\""}, "value": "ValidatorRegistry: reputation out of bounds"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3803c7dd283bafb3c4aa695244f3032e808192a3d4648033a376423cb7e5e604", "typeString": "literal_string \"ValidatorRegistry: reputation out of bounds\""}], "id": 4855, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "3466:7:6", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4864, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3466:174:6", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4865, "nodeType": "ExpressionStatement", "src": "3466:174:6"}, {"expression": {"argumentTypes": null, "id": 4870, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4866, "name": "validatorReputation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4673, "src": "3651:19:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 4868, "indexExpression": {"argumentTypes": null, "id": 4867, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4837, "src": "3671:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3651:31:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 4869, "name": "_newReputation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4839, "src": "3685:14:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3651:48:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 4871, "nodeType": "ExpressionStatement", "src": "3651:48:6"}]}, "documentation": "@dev Updates validator reputation (can be called by market contract).\n@param _validator The address of the validator.\n@param _newReputation The new reputation score.", "id": 4873, "implemented": true, "kind": "function", "modifiers": [{"arguments": [{"argumentTypes": null, "id": 4842, "name": "ADMIN_ROLE", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 41, "src": "3297:10:6", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}], "id": 4843, "modifierName": {"argumentTypes": null, "id": 4841, "name": "only<PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "3288:8:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_bytes32_$", "typeString": "modifier (bytes32)"}}, "nodeType": "ModifierInvocation", "src": "3288:20:6"}, {"arguments": [{"argumentTypes": null, "id": 4845, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4837, "src": "3322:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 4846, "modifierName": {"argumentTypes": null, "id": 4844, "name": "validAddress", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 219, "src": "3309:12:6", "typeDescriptions": {"typeIdentifier": "t_modifier$_t_address_$", "typeString": "modifier (address)"}}, "nodeType": "ModifierInvocation", "src": "3309:24:6"}], "name": "updateValidatorReputation", "nodeType": "FunctionDefinition", "parameters": {"id": 4840, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4837, "name": "_validator", "nodeType": "VariableDeclaration", "scope": 4873, "src": "3224:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4836, "name": "address", "nodeType": "ElementaryTypeName", "src": "3224:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4839, "name": "_newReputation", "nodeType": "VariableDeclaration", "scope": 4873, "src": "3252:22:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4838, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3252:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "3214:66:6"}, "returnParameters": {"id": 4847, "nodeType": "ParameterList", "parameters": [], "src": "3334:0:6"}, "scope": 4931, "src": "3180:526:6", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4893, "nodeType": "Block", "src": "3985:143:6", "statements": [{"expression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 4891, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 4886, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4880, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "4014:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4882, "indexExpression": {"argumentTypes": null, "id": 4881, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4875, "src": "4025:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4014:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4883, "name": "validatorActive", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4669, "src": "4052:15:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4885, "indexExpression": {"argumentTypes": null, "id": 4884, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4875, "src": "4068:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4052:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4014:65:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"argumentTypes": null, "id": 4890, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4095:26:6", "subExpression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4888, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4875, "src": "4110:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 4887, "name": "isBlacklisted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 443, "src": "4096:13:6", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$returns$_t_bool_$", "typeString": "function (address) view returns (bool)"}}, "id": 4889, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4096:25:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4014:107:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 4879, "id": 4892, "nodeType": "Return", "src": "3995:126:6"}]}, "documentation": "@dev Checks if an address is an active validator.\n@param _validator The address to be checked.\n@return A boolean indicating whether the address is an active validator.", "id": 4894, "implemented": true, "kind": "function", "modifiers": [], "name": "isValidator", "nodeType": "FunctionDefinition", "parameters": {"id": 4876, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4875, "name": "_validator", "nodeType": "VariableDeclaration", "scope": 4894, "src": "3938:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4874, "name": "address", "nodeType": "ElementaryTypeName", "src": "3938:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "3937:20:6"}, "returnParameters": {"id": 4879, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4878, "name": "", "nodeType": "VariableDeclaration", "scope": 4894, "src": "3979:4:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 4877, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3979:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "3978:6:6"}, "scope": 4931, "src": "3917:211:6", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 4921, "nodeType": "Block", "src": "4470:199:6", "statements": [{"expression": {"argumentTypes": null, "components": [{"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4907, "name": "validators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4665, "src": "4501:10:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4909, "indexExpression": {"argumentTypes": null, "id": 4908, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4896, "src": "4512:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4501:22:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4910, "name": "validatorActive", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4669, "src": "4537:15:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 4912, "indexExpression": {"argumentTypes": null, "id": 4911, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4896, "src": "4553:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4537:27:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4913, "name": "validatorReputation", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4673, "src": "4578:19:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 4915, "indexExpression": {"argumentTypes": null, "id": 4914, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4896, "src": "4598:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4578:31:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 4916, "name": "validatorJoinDate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4677, "src": "4623:17:6", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 4918, "indexExpression": {"argumentTypes": null, "id": 4917, "name": "_validator", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4896, "src": "4641:10:6", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4623:29:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 4919, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "4487:175:6", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bool_$_t_uint256_$_t_uint256_$", "typeString": "tuple(bool,bool,uint256,uint256)"}}, "functionReturnParameters": 4906, "id": 4920, "nodeType": "Return", "src": "4480:182:6"}]}, "documentation": "@dev Gets validator information.\n@param _validator The address of the validator.\n@return exists, active, reputation, joinDate", "id": 4922, "implemented": true, "kind": "function", "modifiers": [], "name": "getValidatorInfo", "nodeType": "FunctionDefinition", "parameters": {"id": 4897, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4896, "name": "_validator", "nodeType": "VariableDeclaration", "scope": 4922, "src": "4332:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4895, "name": "address", "nodeType": "ElementaryTypeName", "src": "4332:7:6", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "4322:34:6"}, "returnParameters": {"id": 4906, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4899, "name": "exists", "nodeType": "VariableDeclaration", "scope": 4922, "src": "4402:11:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 4898, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4402:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4901, "name": "active", "nodeType": "VariableDeclaration", "scope": 4922, "src": "4415:11:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 4900, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4415:4:6", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4903, "name": "reputation", "nodeType": "VariableDeclaration", "scope": 4922, "src": "4428:18:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4902, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4428:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4905, "name": "joinDate", "nodeType": "VariableDeclaration", "scope": 4922, "src": "4448:16:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4904, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4448:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "4401:64:6"}, "scope": 4931, "src": "4297:372:6", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 4929, "nodeType": "Block", "src": "4860:39:6", "statements": [{"expression": {"argumentTypes": null, "id": 4927, "name": "totalValidators", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4679, "src": "4877:15:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 4926, "id": 4928, "nodeType": "Return", "src": "4870:22:6"}]}, "documentation": "@dev Gets the total number of active validators.\n@return The number of active validators.", "id": 4930, "implemented": true, "kind": "function", "modifiers": [], "name": "getActiveValidatorCount", "nodeType": "FunctionDefinition", "parameters": {"id": 4923, "nodeType": "ParameterList", "parameters": [], "src": "4827:2:6"}, "returnParameters": {"id": 4926, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4925, "name": "", "nodeType": "VariableDeclaration", "scope": 4930, "src": "4851:7:6", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4924, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4851:7:6", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "4850:9:6"}, "scope": 4931, "src": "4795:104:6", "stateMutability": "view", "superFunction": null, "visibility": "public"}], "scope": 4932, "src": "184:4717:6"}], "src": "0:4902:6"}, "legacyAST": {"attributes": {"absolutePath": "project:/contracts/ValidatorRegistry.sol", "exportedSymbols": {"ValidatorRegistry": [4931]}}, "children": [{"attributes": {"literals": ["solidity", "^", "0.5", ".0"]}, "id": 4644, "name": "PragmaDirective", "src": "0:23:6"}, {"attributes": {"SourceUnit": 538, "absolutePath": "project:/contracts/AccessControl.sol", "file": "./AccessControl.sol", "scope": 4932, "symbolAliases": [null], "unitAlias": ""}, "id": 4645, "name": "ImportDirective", "src": "25:29:6"}, {"attributes": {"contractDependencies": [537], "contractKind": "contract", "documentation": "@title ValidatorRegistry\n@dev A contract for managing a registry of validators with comprehensive access control.", "fullyImplemented": true, "linearizedBaseContracts": [4931, 537], "name": "ValidatorRegistry", "scope": 4932}, "children": [{"attributes": {"arguments": null}, "children": [{"attributes": {"contractScope": null, "name": "AccessControl", "referencedDeclaration": 537, "type": "contract AccessControl"}, "id": 4646, "name": "UserDefinedTypeName", "src": "214:13:6"}], "id": 4647, "name": "InheritanceSpecifier", "src": "214:13:6"}, {"attributes": {"anonymous": false, "documentation": null, "name": "ValidatorAdded"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": false, "name": "validator", "scope": 4651, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4648, "name": "ElementaryTypeName", "src": "269:7:6"}], "id": 4649, "name": "VariableDeclaration", "src": "269:17:6"}], "id": 4650, "name": "ParameterList", "src": "268:19:6"}], "id": 4651, "name": "EventDefinition", "src": "248:40:6"}, {"attributes": {"anonymous": false, "documentation": null, "name": "ValidatorRemoved"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": false, "name": "validator", "scope": 4655, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4652, "name": "ElementaryTypeName", "src": "316:7:6"}], "id": 4653, "name": "VariableDeclaration", "src": "316:17:6"}], "id": 4654, "name": "ParameterList", "src": "315:19:6"}], "id": 4655, "name": "EventDefinition", "src": "293:42:6"}, {"attributes": {"anonymous": false, "documentation": null, "name": "ValidatorStatusChanged"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": false, "name": "validator", "scope": 4661, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4656, "name": "ElementaryTypeName", "src": "369:7:6"}], "id": 4657, "name": "VariableDeclaration", "src": "369:17:6"}, {"attributes": {"constant": false, "indexed": false, "name": "active", "scope": 4661, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 4658, "name": "ElementaryTypeName", "src": "388:4:6"}], "id": 4659, "name": "VariableDeclaration", "src": "388:11:6"}], "id": 4660, "name": "ParameterList", "src": "368:32:6"}], "id": 4661, "name": "EventDefinition", "src": "340:61:6"}, {"attributes": {"constant": false, "name": "validators", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => bool)", "value": null, "visibility": "public"}, "children": [{"attributes": {"type": "mapping(address => bool)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 4662, "name": "ElementaryTypeName", "src": "438:7:6"}, {"attributes": {"name": "bool", "type": "bool"}, "id": 4663, "name": "ElementaryTypeName", "src": "449:4:6"}], "id": 4664, "name": "Mapping", "src": "430:24:6"}], "id": 4665, "name": "VariableDeclaration", "src": "430:42:6"}, {"attributes": {"constant": false, "name": "validatorActive", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => bool)", "value": null, "visibility": "public"}, "children": [{"attributes": {"type": "mapping(address => bool)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 4666, "name": "ElementaryTypeName", "src": "486:7:6"}, {"attributes": {"name": "bool", "type": "bool"}, "id": 4667, "name": "ElementaryTypeName", "src": "497:4:6"}], "id": 4668, "name": "Mapping", "src": "478:24:6"}], "id": 4669, "name": "VariableDeclaration", "src": "478:47:6"}, {"attributes": {"constant": false, "name": "validatorReputation", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => uint256)", "value": null, "visibility": "public"}, "children": [{"attributes": {"type": "mapping(address => uint256)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 4670, "name": "ElementaryTypeName", "src": "539:7:6"}, {"attributes": {"name": "uint256", "type": "uint256"}, "id": 4671, "name": "ElementaryTypeName", "src": "550:7:6"}], "id": 4672, "name": "Mapping", "src": "531:27:6"}], "id": 4673, "name": "VariableDeclaration", "src": "531:54:6"}, {"attributes": {"constant": false, "name": "validatorJoinDate", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => uint256)", "value": null, "visibility": "public"}, "children": [{"attributes": {"type": "mapping(address => uint256)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 4674, "name": "ElementaryTypeName", "src": "599:7:6"}, {"attributes": {"name": "uint256", "type": "uint256"}, "id": 4675, "name": "ElementaryTypeName", "src": "610:7:6"}], "id": 4676, "name": "Mapping", "src": "591:27:6"}], "id": 4677, "name": "VariableDeclaration", "src": "591:52:6"}, {"attributes": {"constant": false, "name": "totalValidators", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "public"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4678, "name": "ElementaryTypeName", "src": "650:7:6"}], "id": 4679, "name": "VariableDeclaration", "src": "650:30:6"}, {"attributes": {"constant": true, "name": "MIN_REPUTATION", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "uint256", "visibility": "public"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4680, "name": "ElementaryTypeName", "src": "686:7:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 4681, "name": "Literal", "src": "727:1:6"}], "id": 4682, "name": "VariableDeclaration", "src": "686:42:6"}, {"attributes": {"constant": true, "name": "MAX_REPUTATION", "scope": 4931, "stateVariable": true, "storageLocation": "default", "type": "uint256", "visibility": "public"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4683, "name": "ElementaryTypeName", "src": "734:7:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "31303030", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 1000", "value": "1000"}, "id": 4684, "name": "Literal", "src": "775:4:6"}], "id": 4685, "name": "VariableDeclaration", "src": "734:45:6"}, {"attributes": {"documentation": "@dev Adds a new validator to the registry with comprehensive validation.\n@param _validator The address of the validator to be added.", "implemented": true, "isConstructor": false, "kind": "function", "name": "addValidator", "scope": 4931, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_validator", "scope": 4747, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4686, "name": "ElementaryTypeName", "src": "980:7:6"}], "id": 4687, "name": "VariableDeclaration", "src": "980:18:6"}], "id": 4688, "name": "ParameterList", "src": "970:34:6"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4700, "name": "ParameterList", "src": "1143:0:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "modifier (bytes32)", "value": "only<PERSON><PERSON>"}, "id": 4689, "name": "Identifier", "src": "1028:8:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 41, "type": "bytes32", "value": "ADMIN_ROLE"}, "id": 4690, "name": "Identifier", "src": "1037:10:6"}], "id": 4691, "name": "ModifierInvocation", "src": "1028:20:6"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 162, "type": "modifier ()", "value": "whenNotPaused"}, "id": 4692, "name": "Identifier", "src": "1057:13:6"}], "id": 4693, "name": "ModifierInvocation", "src": "1057:13:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 219, "type": "modifier (address)", "value": "validAddress"}, "id": 4694, "name": "Identifier", "src": "1079:12:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4695, "name": "Identifier", "src": "1092:10:6"}], "id": 4696, "name": "ModifierInvocation", "src": "1079:24:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 185, "type": "modifier (address)", "value": "notBlacklisted"}, "id": 4697, "name": "Identifier", "src": "1112:14:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4698, "name": "Identifier", "src": "1127:10:6"}], "id": 4699, "name": "ModifierInvocation", "src": "1112:26:6"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_7100ff56886f5a774be10aedbf1775a78c96801eb279aad6879fc05a23d84f4b", "typeString": "literal_string \"ValidatorRegistry: validator already exists\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4701, "name": "Identifier", "src": "1153:7:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!", "prefix": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4702, "name": "Identifier", "src": "1175:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4703, "name": "Identifier", "src": "1186:10:6"}], "id": 4704, "name": "IndexAccess", "src": "1175:22:6"}], "id": 4705, "name": "UnaryOperation", "src": "1174:23:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "56616c696461746f7252656769737472793a2076616c696461746f7220616c726561647920657869737473", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ValidatorRegistry: validator already exists\"", "value": "ValidatorRegistry: validator already exists"}, "id": 4706, "name": "Literal", "src": "1211:45:6"}], "id": 4707, "name": "FunctionCall", "src": "1153:113:6"}], "id": 4708, "name": "ExpressionStatement", "src": "1153:113:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4709, "name": "Identifier", "src": "1277:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4710, "name": "Identifier", "src": "1288:10:6"}], "id": 4711, "name": "IndexAccess", "src": "1277:22:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 4712, "name": "Literal", "src": "1302:4:6"}], "id": 4713, "name": "Assignment", "src": "1277:29:6"}], "id": 4714, "name": "ExpressionStatement", "src": "1277:29:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4669, "type": "mapping(address => bool)", "value": "validatorActive"}, "id": 4715, "name": "Identifier", "src": "1316:15:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4716, "name": "Identifier", "src": "1332:10:6"}], "id": 4717, "name": "IndexAccess", "src": "1316:27:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 4718, "name": "Literal", "src": "1346:4:6"}], "id": 4719, "name": "Assignment", "src": "1316:34:6"}], "id": 4720, "name": "ExpressionStatement", "src": "1316:34:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4673, "type": "mapping(address => uint256)", "value": "validatorReputation"}, "id": 4721, "name": "Identifier", "src": "1360:19:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4722, "name": "Identifier", "src": "1380:10:6"}], "id": 4723, "name": "IndexAccess", "src": "1360:31:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "353030", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 500", "value": "500"}, "id": 4724, "name": "Literal", "src": "1394:3:6"}], "id": 4725, "name": "Assignment", "src": "1360:37:6"}], "id": 4726, "name": "ExpressionStatement", "src": "1360:37:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4677, "type": "mapping(address => uint256)", "value": "validatorJoinDate"}, "id": 4727, "name": "Identifier", "src": "1440:17:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4728, "name": "Identifier", "src": "1458:10:6"}], "id": 4729, "name": "IndexAccess", "src": "1440:29:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "timestamp", "referencedDeclaration": null, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4936, "type": "block", "value": "block"}, "id": 4730, "name": "Identifier", "src": "1472:5:6"}], "id": 4731, "name": "MemberAccess", "src": "1472:15:6"}], "id": 4732, "name": "Assignment", "src": "1440:47:6"}], "id": 4733, "name": "ExpressionStatement", "src": "1440:47:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "++", "prefix": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4679, "type": "uint256", "value": "totalValidators"}, "id": 4734, "name": "Identifier", "src": "1497:15:6"}], "id": 4735, "name": "UnaryOperation", "src": "1497:17:6"}], "id": 4736, "name": "ExpressionStatement", "src": "1497:17:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 481, "type": "function (bytes32,address)", "value": "_grantRole"}, "id": 4737, "name": "Identifier", "src": "1557:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 46, "type": "bytes32", "value": "VALIDATOR_ROLE"}, "id": 4738, "name": "Identifier", "src": "1568:14:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4739, "name": "Identifier", "src": "1584:10:6"}], "id": 4740, "name": "FunctionCall", "src": "1557:38:6"}], "id": 4741, "name": "ExpressionStatement", "src": "1557:38:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 4651, "type": "function (address)", "value": "ValidatorAdded"}, "id": 4742, "name": "Identifier", "src": "1611:14:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4687, "type": "address", "value": "_validator"}, "id": 4743, "name": "Identifier", "src": "1626:10:6"}], "id": 4744, "name": "FunctionCall", "src": "1611:26:6"}], "id": 4745, "name": "EmitStatement", "src": "1606:31:6"}], "id": 4746, "name": "Block", "src": "1143:501:6"}], "id": 4747, "name": "FunctionDefinition", "src": "949:695:6"}, {"attributes": {"documentation": "@dev Removes a validator from the registry.\n@param _validator The address of the validator to be removed.", "implemented": true, "isConstructor": false, "kind": "function", "name": "removeValidator", "scope": 4931, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_validator", "scope": 4792, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4748, "name": "ElementaryTypeName", "src": "1820:7:6"}], "id": 4749, "name": "VariableDeclaration", "src": "1820:18:6"}], "id": 4750, "name": "ParameterList", "src": "1810:34:6"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4759, "name": "ParameterList", "src": "1912:0:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "modifier (bytes32)", "value": "only<PERSON><PERSON>"}, "id": 4751, "name": "Identifier", "src": "1852:8:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 41, "type": "bytes32", "value": "ADMIN_ROLE"}, "id": 4752, "name": "Identifier", "src": "1861:10:6"}], "id": 4753, "name": "ModifierInvocation", "src": "1852:20:6"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 162, "type": "modifier ()", "value": "whenNotPaused"}, "id": 4754, "name": "Identifier", "src": "1873:13:6"}], "id": 4755, "name": "ModifierInvocation", "src": "1873:13:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 219, "type": "modifier (address)", "value": "validAddress"}, "id": 4756, "name": "Identifier", "src": "1887:12:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4749, "type": "address", "value": "_validator"}, "id": 4757, "name": "Identifier", "src": "1900:10:6"}], "id": 4758, "name": "ModifierInvocation", "src": "1887:24:6"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4760, "name": "Identifier", "src": "1922:7:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4761, "name": "Identifier", "src": "1943:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4749, "type": "address", "value": "_validator"}, "id": 4762, "name": "Identifier", "src": "1954:10:6"}], "id": 4763, "name": "IndexAccess", "src": "1943:22:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "56616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ValidatorRegistry: validator does not exist\"", "value": "ValidatorRegistry: validator does not exist"}, "id": 4764, "name": "Literal", "src": "1979:45:6"}], "id": 4765, "name": "FunctionCall", "src": "1922:112:6"}], "id": 4766, "name": "ExpressionStatement", "src": "1922:112:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4767, "name": "Identifier", "src": "2045:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4749, "type": "address", "value": "_validator"}, "id": 4768, "name": "Identifier", "src": "2056:10:6"}], "id": 4769, "name": "IndexAccess", "src": "2045:22:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "66616c7365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "false"}, "id": 4770, "name": "Literal", "src": "2070:5:6"}], "id": 4771, "name": "Assignment", "src": "2045:30:6"}], "id": 4772, "name": "ExpressionStatement", "src": "2045:30:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4669, "type": "mapping(address => bool)", "value": "validatorActive"}, "id": 4773, "name": "Identifier", "src": "2085:15:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4749, "type": "address", "value": "_validator"}, "id": 4774, "name": "Identifier", "src": "2101:10:6"}], "id": 4775, "name": "IndexAccess", "src": "2085:27:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "66616c7365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "false"}, "id": 4776, "name": "Literal", "src": "2115:5:6"}], "id": 4777, "name": "Assignment", "src": "2085:35:6"}], "id": 4778, "name": "ExpressionStatement", "src": "2085:35:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "--", "prefix": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4679, "type": "uint256", "value": "totalValidators"}, "id": 4779, "name": "Identifier", "src": "2130:15:6"}], "id": 4780, "name": "UnaryOperation", "src": "2130:17:6"}], "id": 4781, "name": "ExpressionStatement", "src": "2130:17:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 510, "type": "function (bytes32,address)", "value": "_revokeRole"}, "id": 4782, "name": "Identifier", "src": "2191:11:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 46, "type": "bytes32", "value": "VALIDATOR_ROLE"}, "id": 4783, "name": "Identifier", "src": "2203:14:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4749, "type": "address", "value": "_validator"}, "id": 4784, "name": "Identifier", "src": "2219:10:6"}], "id": 4785, "name": "FunctionCall", "src": "2191:39:6"}], "id": 4786, "name": "ExpressionStatement", "src": "2191:39:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 4655, "type": "function (address)", "value": "ValidatorRemoved"}, "id": 4787, "name": "Identifier", "src": "2246:16:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4749, "type": "address", "value": "_validator"}, "id": 4788, "name": "Identifier", "src": "2263:10:6"}], "id": 4789, "name": "FunctionCall", "src": "2246:28:6"}], "id": 4790, "name": "EmitStatement", "src": "2241:33:6"}], "id": 4791, "name": "Block", "src": "1912:369:6"}], "id": 4792, "name": "FunctionDefinition", "src": "1786:495:6"}, {"attributes": {"documentation": "@dev Activates or deactivates a validator.\n@param _validator The address of the validator.\n@param _active The new active status.", "implemented": true, "isConstructor": false, "kind": "function", "name": "setValidatorStatus", "scope": 4931, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_validator", "scope": 4835, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4793, "name": "ElementaryTypeName", "src": "2490:7:6"}], "id": 4794, "name": "VariableDeclaration", "src": "2490:18:6"}, {"attributes": {"constant": false, "name": "_active", "scope": 4835, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 4795, "name": "ElementaryTypeName", "src": "2518:4:6"}], "id": 4796, "name": "VariableDeclaration", "src": "2518:12:6"}], "id": 4797, "name": "ParameterList", "src": "2480:56:6"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4806, "name": "ParameterList", "src": "2604:0:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "modifier (bytes32)", "value": "only<PERSON><PERSON>"}, "id": 4798, "name": "Identifier", "src": "2544:8:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 41, "type": "bytes32", "value": "ADMIN_ROLE"}, "id": 4799, "name": "Identifier", "src": "2553:10:6"}], "id": 4800, "name": "ModifierInvocation", "src": "2544:20:6"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 162, "type": "modifier ()", "value": "whenNotPaused"}, "id": 4801, "name": "Identifier", "src": "2565:13:6"}], "id": 4802, "name": "ModifierInvocation", "src": "2565:13:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 219, "type": "modifier (address)", "value": "validAddress"}, "id": 4803, "name": "Identifier", "src": "2579:12:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4794, "type": "address", "value": "_validator"}, "id": 4804, "name": "Identifier", "src": "2592:10:6"}], "id": 4805, "name": "ModifierInvocation", "src": "2579:24:6"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4807, "name": "Identifier", "src": "2614:7:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4808, "name": "Identifier", "src": "2635:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4794, "type": "address", "value": "_validator"}, "id": 4809, "name": "Identifier", "src": "2646:10:6"}], "id": 4810, "name": "IndexAccess", "src": "2635:22:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "56616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ValidatorRegistry: validator does not exist\"", "value": "ValidatorRegistry: validator does not exist"}, "id": 4811, "name": "Literal", "src": "2671:45:6"}], "id": 4812, "name": "FunctionCall", "src": "2614:112:6"}], "id": 4813, "name": "ExpressionStatement", "src": "2614:112:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_da30e9c7e95ccbe8d139701698b417bd0b410f829de465171ce2c4b9af9c7560", "typeString": "literal_string \"ValidatorRegistry: status already set\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4814, "name": "Identifier", "src": "2736:7:6"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4669, "type": "mapping(address => bool)", "value": "validatorActive"}, "id": 4815, "name": "Identifier", "src": "2757:15:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4794, "type": "address", "value": "_validator"}, "id": 4816, "name": "Identifier", "src": "2773:10:6"}], "id": 4817, "name": "IndexAccess", "src": "2757:27:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4796, "type": "bool", "value": "_active"}, "id": 4818, "name": "Identifier", "src": "2788:7:6"}], "id": 4819, "name": "BinaryOperation", "src": "2757:38:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "56616c696461746f7252656769737472793a2073746174757320616c726561647920736574", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ValidatorRegistry: status already set\"", "value": "ValidatorRegistry: status already set"}, "id": 4820, "name": "Literal", "src": "2809:39:6"}], "id": 4821, "name": "FunctionCall", "src": "2736:122:6"}], "id": 4822, "name": "ExpressionStatement", "src": "2736:122:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4669, "type": "mapping(address => bool)", "value": "validatorActive"}, "id": 4823, "name": "Identifier", "src": "2869:15:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4794, "type": "address", "value": "_validator"}, "id": 4824, "name": "Identifier", "src": "2885:10:6"}], "id": 4825, "name": "IndexAccess", "src": "2869:27:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4796, "type": "bool", "value": "_active"}, "id": 4826, "name": "Identifier", "src": "2899:7:6"}], "id": 4827, "name": "Assignment", "src": "2869:37:6"}], "id": 4828, "name": "ExpressionStatement", "src": "2869:37:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [null], "referencedDeclaration": 4661, "type": "function (address,bool)", "value": "ValidatorStatusChanged"}, "id": 4829, "name": "Identifier", "src": "2921:22:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4794, "type": "address", "value": "_validator"}, "id": 4830, "name": "Identifier", "src": "2944:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4796, "type": "bool", "value": "_active"}, "id": 4831, "name": "Identifier", "src": "2956:7:6"}], "id": 4832, "name": "FunctionCall", "src": "2921:43:6"}], "id": 4833, "name": "EmitStatement", "src": "2916:48:6"}], "id": 4834, "name": "Block", "src": "2604:367:6"}], "id": 4835, "name": "FunctionDefinition", "src": "2453:518:6"}, {"attributes": {"documentation": "@dev Updates validator reputation (can be called by market contract).\n@param _validator The address of the validator.\n@param _newReputation The new reputation score.", "implemented": true, "isConstructor": false, "kind": "function", "name": "updateValidatorReputation", "scope": 4931, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_validator", "scope": 4873, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4836, "name": "ElementaryTypeName", "src": "3224:7:6"}], "id": 4837, "name": "VariableDeclaration", "src": "3224:18:6"}, {"attributes": {"constant": false, "name": "_newReputation", "scope": 4873, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4838, "name": "ElementaryTypeName", "src": "3252:7:6"}], "id": 4839, "name": "VariableDeclaration", "src": "3252:22:6"}], "id": 4840, "name": "ParameterList", "src": "3214:66:6"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4847, "name": "ParameterList", "src": "3334:0:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "modifier (bytes32)", "value": "only<PERSON><PERSON>"}, "id": 4841, "name": "Identifier", "src": "3288:8:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 41, "type": "bytes32", "value": "ADMIN_ROLE"}, "id": 4842, "name": "Identifier", "src": "3297:10:6"}], "id": 4843, "name": "ModifierInvocation", "src": "3288:20:6"}, {"children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 219, "type": "modifier (address)", "value": "validAddress"}, "id": 4844, "name": "Identifier", "src": "3309:12:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4837, "type": "address", "value": "_validator"}, "id": 4845, "name": "Identifier", "src": "3322:10:6"}], "id": 4846, "name": "ModifierInvocation", "src": "3309:24:6"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_398b24efb42dfafc989bc950e86a28fbc28bf60479a29837be9db40c245a6ecb", "typeString": "literal_string \"ValidatorRegistry: validator does not exist\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4848, "name": "Identifier", "src": "3344:7:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4849, "name": "Identifier", "src": "3365:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4837, "type": "address", "value": "_validator"}, "id": 4850, "name": "Identifier", "src": "3376:10:6"}], "id": 4851, "name": "IndexAccess", "src": "3365:22:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "56616c696461746f7252656769737472793a2076616c696461746f7220646f6573206e6f74206578697374", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ValidatorRegistry: validator does not exist\"", "value": "ValidatorRegistry: validator does not exist"}, "id": 4852, "name": "Literal", "src": "3401:45:6"}], "id": 4853, "name": "FunctionCall", "src": "3344:112:6"}], "id": 4854, "name": "ExpressionStatement", "src": "3344:112:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3803c7dd283bafb3c4aa695244f3032e808192a3d4648033a376423cb7e5e604", "typeString": "literal_string \"ValidatorRegistry: reputation out of bounds\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4855, "name": "Identifier", "src": "3466:7:6"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "&&", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": ">=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4839, "type": "uint256", "value": "_newReputation"}, "id": 4856, "name": "Identifier", "src": "3487:14:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4682, "type": "uint256", "value": "MIN_REPUTATION"}, "id": 4857, "name": "Identifier", "src": "3505:14:6"}], "id": 4858, "name": "BinaryOperation", "src": "3487:32:6"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4839, "type": "uint256", "value": "_newReputation"}, "id": 4859, "name": "Identifier", "src": "3539:14:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4685, "type": "uint256", "value": "MAX_REPUTATION"}, "id": 4860, "name": "Identifier", "src": "3557:14:6"}], "id": 4861, "name": "BinaryOperation", "src": "3539:32:6"}], "id": 4862, "name": "BinaryOperation", "src": "3487:84:6"}, {"attributes": {"argumentTypes": null, "hexvalue": "56616c696461746f7252656769737472793a2072657075746174696f6e206f7574206f6620626f756e6473", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ValidatorRegistry: reputation out of bounds\"", "value": "ValidatorRegistry: reputation out of bounds"}, "id": 4863, "name": "Literal", "src": "3585:45:6"}], "id": 4864, "name": "FunctionCall", "src": "3466:174:6"}], "id": 4865, "name": "ExpressionStatement", "src": "3466:174:6"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4673, "type": "mapping(address => uint256)", "value": "validatorReputation"}, "id": 4866, "name": "Identifier", "src": "3651:19:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4837, "type": "address", "value": "_validator"}, "id": 4867, "name": "Identifier", "src": "3671:10:6"}], "id": 4868, "name": "IndexAccess", "src": "3651:31:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4839, "type": "uint256", "value": "_newReputation"}, "id": 4869, "name": "Identifier", "src": "3685:14:6"}], "id": 4870, "name": "Assignment", "src": "3651:48:6"}], "id": 4871, "name": "ExpressionStatement", "src": "3651:48:6"}], "id": 4872, "name": "Block", "src": "3334:372:6"}], "id": 4873, "name": "FunctionDefinition", "src": "3180:526:6"}, {"attributes": {"documentation": "@dev Checks if an address is an active validator.\n@param _validator The address to be checked.\n@return A boolean indicating whether the address is an active validator.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "isValidator", "scope": 4931, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_validator", "scope": 4894, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4874, "name": "ElementaryTypeName", "src": "3938:7:6"}], "id": 4875, "name": "VariableDeclaration", "src": "3938:18:6"}], "id": 4876, "name": "ParameterList", "src": "3937:20:6"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 4894, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 4877, "name": "ElementaryTypeName", "src": "3979:4:6"}], "id": 4878, "name": "VariableDeclaration", "src": "3979:4:6"}], "id": 4879, "name": "ParameterList", "src": "3978:6:6"}, {"children": [{"attributes": {"functionReturnParameters": 4879}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "&&", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "&&", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4880, "name": "Identifier", "src": "4014:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4875, "type": "address", "value": "_validator"}, "id": 4881, "name": "Identifier", "src": "4025:10:6"}], "id": 4882, "name": "IndexAccess", "src": "4014:22:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4669, "type": "mapping(address => bool)", "value": "validatorActive"}, "id": 4883, "name": "Identifier", "src": "4052:15:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4875, "type": "address", "value": "_validator"}, "id": 4884, "name": "Identifier", "src": "4068:10:6"}], "id": 4885, "name": "IndexAccess", "src": "4052:27:6"}], "id": 4886, "name": "BinaryOperation", "src": "4014:65:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!", "prefix": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "bool", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 443, "type": "function (address) view returns (bool)", "value": "isBlacklisted"}, "id": 4887, "name": "Identifier", "src": "4096:13:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4875, "type": "address", "value": "_validator"}, "id": 4888, "name": "Identifier", "src": "4110:10:6"}], "id": 4889, "name": "FunctionCall", "src": "4096:25:6"}], "id": 4890, "name": "UnaryOperation", "src": "4095:26:6"}], "id": 4891, "name": "BinaryOperation", "src": "4014:107:6"}], "id": 4892, "name": "Return", "src": "3995:126:6"}], "id": 4893, "name": "Block", "src": "3985:143:6"}], "id": 4894, "name": "FunctionDefinition", "src": "3917:211:6"}, {"attributes": {"documentation": "@dev Gets validator information.\n@param _validator The address of the validator.\n@return exists, active, reputation, joinDate", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getValidatorInfo", "scope": 4931, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_validator", "scope": 4922, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4895, "name": "ElementaryTypeName", "src": "4332:7:6"}], "id": 4896, "name": "VariableDeclaration", "src": "4332:18:6"}], "id": 4897, "name": "ParameterList", "src": "4322:34:6"}, {"children": [{"attributes": {"constant": false, "name": "exists", "scope": 4922, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 4898, "name": "ElementaryTypeName", "src": "4402:4:6"}], "id": 4899, "name": "VariableDeclaration", "src": "4402:11:6"}, {"attributes": {"constant": false, "name": "active", "scope": 4922, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 4900, "name": "ElementaryTypeName", "src": "4415:4:6"}], "id": 4901, "name": "VariableDeclaration", "src": "4415:11:6"}, {"attributes": {"constant": false, "name": "reputation", "scope": 4922, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4902, "name": "ElementaryTypeName", "src": "4428:7:6"}], "id": 4903, "name": "VariableDeclaration", "src": "4428:18:6"}, {"attributes": {"constant": false, "name": "joinDate", "scope": 4922, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4904, "name": "ElementaryTypeName", "src": "4448:7:6"}], "id": 4905, "name": "VariableDeclaration", "src": "4448:16:6"}], "id": 4906, "name": "ParameterList", "src": "4401:64:6"}, {"children": [{"attributes": {"functionReturnParameters": 4906}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "type": "tuple(bool,bool,uint256,uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4665, "type": "mapping(address => bool)", "value": "validators"}, "id": 4907, "name": "Identifier", "src": "4501:10:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4896, "type": "address", "value": "_validator"}, "id": 4908, "name": "Identifier", "src": "4512:10:6"}], "id": 4909, "name": "IndexAccess", "src": "4501:22:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4669, "type": "mapping(address => bool)", "value": "validatorActive"}, "id": 4910, "name": "Identifier", "src": "4537:15:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4896, "type": "address", "value": "_validator"}, "id": 4911, "name": "Identifier", "src": "4553:10:6"}], "id": 4912, "name": "IndexAccess", "src": "4537:27:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4673, "type": "mapping(address => uint256)", "value": "validatorReputation"}, "id": 4913, "name": "Identifier", "src": "4578:19:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4896, "type": "address", "value": "_validator"}, "id": 4914, "name": "Identifier", "src": "4598:10:6"}], "id": 4915, "name": "IndexAccess", "src": "4578:31:6"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4677, "type": "mapping(address => uint256)", "value": "validatorJoinDate"}, "id": 4916, "name": "Identifier", "src": "4623:17:6"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4896, "type": "address", "value": "_validator"}, "id": 4917, "name": "Identifier", "src": "4641:10:6"}], "id": 4918, "name": "IndexAccess", "src": "4623:29:6"}], "id": 4919, "name": "TupleExpression", "src": "4487:175:6"}], "id": 4920, "name": "Return", "src": "4480:182:6"}], "id": 4921, "name": "Block", "src": "4470:199:6"}], "id": 4922, "name": "FunctionDefinition", "src": "4297:372:6"}, {"attributes": {"documentation": "@dev Gets the total number of active validators.\n@return The number of active validators.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getActiveValidatorCount", "scope": 4931, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 4923, "name": "ParameterList", "src": "4827:2:6"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 4930, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4924, "name": "ElementaryTypeName", "src": "4851:7:6"}], "id": 4925, "name": "VariableDeclaration", "src": "4851:7:6"}], "id": 4926, "name": "ParameterList", "src": "4850:9:6"}, {"children": [{"attributes": {"functionReturnParameters": 4926}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4679, "type": "uint256", "value": "totalValidators"}, "id": 4927, "name": "Identifier", "src": "4877:15:6"}], "id": 4928, "name": "Return", "src": "4870:22:6"}], "id": 4929, "name": "Block", "src": "4860:39:6"}], "id": 4930, "name": "FunctionDefinition", "src": "4795:104:6"}], "id": 4931, "name": "ContractDefinition", "src": "184:4717:6"}], "id": 4932, "name": "SourceUnit", "src": "0:4902:6"}, "compiler": {"name": "solc", "version": "0.5.17+commit.d19bba13.Emscripten.clang"}, "networks": {"5777": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0xce9052db3af1d6b0b4fd3972c63b9cba4a49c0ebe72e3d0c759a3ccfc2c38e25"}}, "schemaVersion": "3.4.16", "updatedAt": "2025-06-23T09:15:20.632Z", "devdoc": {"details": "A contract for managing a registry of validators with comprehensive access control.", "methods": {"addValidator(address)": {"details": "Adds a new validator to the registry with comprehensive validation.", "params": {"_validator": "The address of the validator to be added."}}, "blacklistAccount(address)": {"details": "Add address to blacklist"}, "getActiveValidatorCount()": {"details": "Gets the total number of active validators.", "return": "The number of active validators."}, "getRoleAdmin(bytes32)": {"details": "Get the admin role that controls a role"}, "getValidatorInfo(address)": {"details": "Gets validator information.", "params": {"_validator": "The address of the validator."}, "return": "exists, active, reputation, joinDate"}, "grantRole(bytes32,address)": {"details": "Grant a role to an account"}, "hasRole(bytes32,address)": {"details": "Check if an account has a specific role"}, "isBlacklisted(address)": {"details": "Check if account is blacklisted"}, "isValidator(address)": {"details": "Checks if an address is an active validator.", "params": {"_validator": "The address to be checked."}, "return": "A boolean indicating whether the address is an active validator."}, "owner()": {"details": "Get contract owner"}, "pause()": {"details": "Pause the contract (emergency stop)"}, "paused()": {"details": "Check if contract is paused"}, "removeFromBlacklist(address)": {"details": "Remove address from blacklist"}, "removeValidator(address)": {"details": "Removes a validator from the registry.", "params": {"_validator": "The address of the validator to be removed."}}, "renounceRole(bytes32,address)": {"details": "Renounce a role (caller renounces their own role)"}, "revokeRole(bytes32,address)": {"details": "Revoke a role from an account"}, "setValidatorStatus(address,bool)": {"details": "Activates or deactivates a validator.", "params": {"_active": "The new active status.", "_validator": "The address of the validator."}}, "unpause()": {"details": "Unpause the contract"}, "updateValidatorReputation(address,uint256)": {"details": "Updates validator reputation (can be called by market contract).", "params": {"_newReputation": "The new reputation score.", "_validator": "The address of the validator."}}}, "title": "ValidatorRegistry"}, "userdoc": {"methods": {}}}