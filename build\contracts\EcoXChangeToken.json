{"contractName": "EcoXChangeToken", "abi": [{"inputs": [{"internalType": "address", "name": "_erc20TokenAddress", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amt", "type": "uint256"}], "name": "EXCMinted", "type": "event"}, {"constant": false, "inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amtOfEXC", "type": "uint256"}], "name": "getEXC", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": true, "stateMutability": "payable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "ad", "type": "address"}], "name": "checkEXC", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amt", "type": "uint256"}], "name": "transferEXC", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amt", "type": "uint256"}], "name": "transferEXCFrom", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "tokenOwner", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}], "name": "destroyEXC", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.5.17+commit.d19bba13\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_erc20TokenAddress\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amt\",\"type\":\"uint256\"}],\"name\":\"EXCMinted\",\"type\":\"event\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"ad\",\"type\":\"address\"}],\"name\":\"checkEXC\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenOwner\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenAmount\",\"type\":\"uint256\"}],\"name\":\"destroyEXC\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amtOfEXC\",\"type\":\"uint256\"}],\"name\":\"getEXC\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":true,\"stateMutability\":\"payable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amt\",\"type\":\"uint256\"}],\"name\":\"transferEXC\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amt\",\"type\":\"uint256\"}],\"name\":\"transferEXCFrom\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"A token contract representing EcoXChange Tokens.\",\"methods\":{\"checkEXC(address)\":{\"details\":\"Function to check the credit of the owner\",\"params\":{\"ad\":\"address of the owner\"},\"return\":\"uint256 credit of the owner\"},\"destroyEXC(address,uint256)\":{\"details\":\"Function to destroy EXC\",\"params\":{\"tokenAmount\":\"amount of EXC to destroy\",\"tokenOwner\":\"address of the owner\"},\"return\":\"uint256 amount of EXC destroyed\"},\"getEXC(address,uint256)\":{\"details\":\"Function to mint EcoXChange Tokens (EXC) to the recipient.\",\"params\":{\"amtOfEXC\":\"Amount of EXC to mint.\",\"recipient\":\"Address of the recipient that will receive the EXC.\"},\"return\":\"The amount of EXC minted.\"},\"transferEXC(address,uint256)\":{\"details\":\"Function to transfer EXC from the owner to the recipient\",\"params\":{\"amt\":\"amount of EXC to transfer\",\"recipient\":\"address of the recipient\"}},\"transferEXCFrom(address,address,uint256)\":{\"details\":\"Function to transfer EXC from the owner to the recipient\",\"params\":{\"amt\":\"amount of EXC to transfer\",\"recipient\":\"address of the recipient\",\"sender\":\"address of the sender\"}}},\"title\":\"EcoXChangeToken\"},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"project:/contracts/EcoXChangeToken.sol\":\"EcoXChangeToken\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/ERC20.sol\":{\"keccak256\":\"0x8cbb2a43af37209caeb39903b2e45a7d7276f102ad83283a7eb118cb3fa7d591\",\"urls\":[\"bzz-raw://9e349590ff1f0b362f812af1017e5ff3d23758e20d55780a3ea03d9deab8cee6\",\"dweb:/ipfs/QmQkBgdLNCHCd7DZ4ToUTAMxFPi7pz9hxG2TMG2pKfAiPi\"]},\"project:/contracts/EcoXChangeToken.sol\":{\"keccak256\":\"0x636ae90d43065fc4756769322fc1c7a7ae7473f2ff1dfe8a025e5eb2621db631\",\"urls\":[\"bzz-raw://6d7ed92e625768c45a682fcdb7c45019f9b32d9aa5ab5c340ab9f289904b20ce\",\"dweb:/ipfs/QmXvfU7boqQkfobcQDa7zQNTGt74Dae5fnKhQHBQtGEynZ\"]}},\"version\":1}", "bytecode": "0x608060405233600160006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555034801561005157600080fd5b50604051611b10380380611b108339818101604052602081101561007457600080fd5b810190808051906020019092919050505060006040516100939061013a565b604051809103906000f0801580156100af573d6000803e3d6000fd5b509050806000806101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555033600160006101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055505050610147565b61110280610a0e83390190565b6108b8806101566000396000f3fe60806040526004361061004a5760003560e01c80639a46bcbb1461004f578063b73cacc5146100be578063c5eaea2c14610120578063d8ce4a031461019b578063e3491a6c14610200575b600080fd5b34801561005b57600080fd5b506100a86004803603604081101561007257600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291908035906020019092919050505061025b565b6040518082815260200191505060405180910390f35b61010a600480360360408110156100d457600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506103c8565b6040518082815260200191505060405180910390f35b34801561012c57600080fd5b506101996004803603606081101561014357600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190505050610597565b005b3480156101a757600080fd5b506101ea600480360360208110156101be57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506106b4565b6040518082815260200191505060405180910390f35b34801561020c57600080fd5b506102596004803603604081101561022357600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff1690602001909291908035906020019092919050505061079b565b005b600081610267846106b4565b10156102db576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260188152602001807f496e73756666696369656e742045584320746f206275726e000000000000000081525060200191505060405180910390fd5b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16639dc29fac84846040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050602060405180830381600087803b15801561038357600080fd5b505af1158015610397573d6000803e3d6000fd5b505050506040513d60208110156103ad57600080fd5b81019080805190602001909291905050505081905092915050565b600080821161043f576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260208152602001807f416d6f756e74206d7573742062652067726561746572207468616e207a65726f81525060200191505060405180910390fd5b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166340c10f1984846040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050602060405180830381600087803b1580156104e757600080fd5b505af11580156104fb573d6000803e3d6000fd5b505050506040513d602081101561051157600080fd5b8101908080519060200190929190505050507fe3108c46fd22b8024177231ae07b20155534c9cc8529e74ad5a748d483c6ba7f8383604051808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018281526020019250505060405180910390a181905092915050565b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166323b872dd8484846040518463ffffffff1660e01b8152600401808473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020018281526020019350505050602060405180830381600087803b15801561067357600080fd5b505af1158015610687573d6000803e3d6000fd5b505050506040513d602081101561069d57600080fd5b810190808051906020019092919050505050505050565b6000806000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166370a08231846040518263ffffffff1660e01b8152600401808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060206040518083038186803b15801561075557600080fd5b505afa158015610769573d6000803e3d6000fd5b505050506040513d602081101561077f57600080fd5b8101908080519060200190929190505050905080915050919050565b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1663a9059cbb83836040518363ffffffff1660e01b8152600401808373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200182815260200192505050602060405180830381600087803b15801561084357600080fd5b505af1158015610857573d6000803e3d6000fd5b505050506040513d602081101561086d57600080fd5b810190808051906020019092919050505050505056fea265627a7a723158200f4d7e6d3e13825ac3774fd9dfb52baa495a64fedf52d6604125bb6c6e6ee72864736f6c63430005110032608060405260008060006101000a81548160ff02191690831515021790555033600060016101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555034801561006b57600080fd5b506110878061007b6000396000f3fe608060405234801561001057600080fd5b50600436106100a95760003560e01c806370a082311161007157806370a0823114610263578063893d20e8146102bb5780638da5cb5b1461030557806395d89b411461034f5780639dc29fac146103d2578063a9059cbb14610438576100a9565b806305d2035b146100ae57806306fdde03146100d057806323b872dd14610153578063313ce567146101d957806340c10f19146101fd575b600080fd5b6100b661049e565b604051808215151515815260200191505060405180910390f35b6100d86104b0565b6040518080602001828103825283818151815260200191508051906020019080838360005b838110156101185780820151818401526020810190506100fd565b50505050905090810190601f1680156101455780820380516001836020036101000a031916815260200191505b509250505060405180910390f35b6101bf6004803603606081101561016957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803590602001909291905050506104e9565b604051808215151515815260200191505060405180910390f35b6101e1610974565b604051808260ff1660ff16815260200191505060405180910390f35b6102496004803603604081101561021357600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190505050610979565b604051808215151515815260200191505060405180910390f35b6102a56004803603602081101561027957600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610b5b565b6040518082815260200191505060405180910390f35b6102c3610ba4565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b61030d610bcd565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b610357610bf3565b6040518080602001828103825283818151815260200191508051906020019080838360005b8381101561039757808201518184015260208101905061037c565b50505050905090810190601f1680156103c45780820380516001836020036101000a031916815260200191505b509250505060405180910390f35b61041e600480360360408110156103e857600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190505050610c2c565b604051808215151515815260200191505060405180910390f35b6104846004803603604081101561044e57600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190505050610d8f565b604051808215151515815260200191505060405180910390f35b6000809054906101000a900460ff1681565b6040518060400160405280601081526020017f45636f584368616e676520546f6b656e0000000000000000000000000000000081525081565b60008073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff16141561052457600080fd5b600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020548211156105d9576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004018080602001828103825260208152602001807f46726f6d20646f65736e2774206861766520656e6f7567682062616c616e636581525060200191505060405180910390fd5b600160008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060003273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020548211156106cb576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252601e8152602001807f4e6f7420616c6c6f77656420746f207370656e642074686973206d756368000081525060200191505060405180910390fd5b61071d82600260008773ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054610ffb90919063ffffffff16565b600260008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055506107b282600260008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000205461101290919063ffffffff16565b600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000208190555061088482600160008773ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060003273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054610ffb90919063ffffffff16565b600160008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002060003273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055508273ffffffffffffffffffffffffffffffffffffffff168473ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040518082815260200191505060405180910390a3600190509392505050565b601281565b60008060019054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16146109d457600080fd5b6000809054906101000a900460ff16156109ed57600080fd5b610a028260035461101290919063ffffffff16565b600381905550610a5a82600260008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000205461101290919063ffffffff16565b600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055508273ffffffffffffffffffffffffffffffffffffffff167f0f6798a560793a54c3bcfe86a93cde1e73087d944c0ea20544137d4121396885836040518082815260200191505060405180910390a28273ffffffffffffffffffffffffffffffffffffffff16600073ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040518082815260200191505060405180910390a36001905092915050565b6000600260008373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020549050919050565b60008060019054906101000a900473ffffffffffffffffffffffffffffffffffffffff16905090565b600060019054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b6040518060400160405280600381526020017f455843000000000000000000000000000000000000000000000000000000000081525081565b60008060019054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610c8757600080fd5b610c9c82600354610ffb90919063ffffffff16565b600381905550610cf482600260008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054610ffb90919063ffffffff16565b600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055508273ffffffffffffffffffffffffffffffffffffffff167fcc16f5dbb4873280815c1ee09dbd06736cffcc184412cf7a71a0fdb75d397ca5836040518082815260200191505060405180910390a26001905092915050565b60008073ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff161415610dca57600080fd5b600260003273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054821115610e62576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040180806020018281038252602681526020018061102d6026913960400191505060405180910390fd5b610eb482600260003273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002054610ffb90919063ffffffff16565b600260003273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200190815260200160002081905550610f4982600260008673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020016000205461101290919063ffffffff16565b600260008573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168152602001908152602001600020819055508273ffffffffffffffffffffffffffffffffffffffff163273ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040518082815260200191505060405180910390a36001905092915050565b60008282111561100757fe5b818303905092915050565b600081830190508281101561102357fe5b8090509291505056fe6d73672e73656e64657220646f65736e2774206861766520656e6f7567682062616c616e6365a265627a7a723158202cbdc436485b87d4b44e9305184f97f1e98611c6f6c6f0bf644042152f98776b64736f6c63430005110032", "deployedBytecode": "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", "sourceMap": "139:2557:5:-;;;302:10;286:26;;;;;;;;;;;;;;;;;;;;319:140;8:9:-1;5:2;;;30:1;27;20:12;5:2;319:140:5;;;;;;;;;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;319:140:5;;;;;;;;;;;;;;;;376:7;386:11;;;;;:::i;:::-;;;;;;;;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;386:11:5;376:21;;423:1;407:13;;:17;;;;;;;;;;;;;;;;;;442:10;434:5;;:18;;;;;;;;;;;;;;;;;;319:140;;139:2557;;;;;;;;;;:::o;:::-;;;;;;;", "deployedSourceMap": "139:2557:5:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2382:312;;8:9:-1;5:2;;;30:1;27;20:12;5:2;2382:312:5;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;2382:312:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;715:361;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;715:361:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1953:227;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1953:227:5;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1953:227:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;1233:144;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1233:144:5;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1233:144:5;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;1561:165;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1561:165:5;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1561:165:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;2382:312;2481:7;2545:11;2521:20;2530:10;2521:8;:20::i;:::-;:35;;2500:106;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2616:13;;;;;;;;;;;:18;;;2635:10;2647:11;2616:43;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;2616:43:5;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;2616:43:5;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;2616:43:5;;;;;;;;;;;;;;;;;2676:11;2669:18;;2382:312;;;;:::o;715:361::-;814:7;852:1;841:8;:12;833:57;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;960:13;;;;;;;;;;;:18;;;979:9;990:8;960:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;960:39:5;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;960:39:5;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;960:39:5;;;;;;;;;;;;;;;;;1014:30;1024:9;1035:8;1014:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;1061:8;1054:15;;715:361;;;;:::o;1953:227::-;2123:13;;;;;;;;;;;:26;;;2150:6;2158:9;2169:3;2123:50;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;2123:50:5;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;2123:50:5;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;2123:50:5;;;;;;;;;;;;;;;;;1953:227;;;:::o;1233:144::-;1284:7;1303:14;1320:13;;;;;;;;;;;:23;;;1344:2;1320:27;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1320:27:5;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;1320:27:5;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1320:27:5;;;;;;;;;;;;;;;;1303:44;;1364:6;1357:13;;;1233:144;;;:::o;1561:165::-;1681:13;;;;;;;;;;;:22;;;1704:9;1715:3;1681:38;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;1681:38:5;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;1681:38:5;;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;1681:38:5;;;;;;;;;;;;;;;;;1561:165;;:::o", "source": "pragma solidity ^0.5.0;\n\nimport \"./ERC20.sol\";\n\n/**\n * @title EcoXChangeToken\n * @dev A token contract representing EcoXChange Tokens.\n */\ncontract EcoXChangeToken {\n    // Events\n    event EXCMinted(address recipient, uint256 amt);\n\n    // State variables\n    ERC20 erc20Contract;\n    address owner = msg.sender;\n\n    constructor(address _erc20TokenAddress) public {\n        ERC20 e = new ERC20();\n        erc20Contract = e;\n        owner = msg.sender;\n    }\n\n    /**\n     * @dev Function to mint EcoXChange Tokens (EXC) to the recipient.\n     * @param recipient Address of the recipient that will receive the EXC.\n     * @param amtOfEXC Amount of EXC to mint.\n     * @return The amount of EXC minted.\n     */\n    function getEXC(\n        address recipient,\n        uint256 amtOfEXC\n    ) public payable returns (uint256) {\n        require(amtOfEXC > 0, \"Amount must be greater than zero\");\n        //1 ether = 1 EXC, no need to convert weiAmt to EXC\n        erc20Contract.mint(recipient, amtOfEXC);\n        emit EXCMinted(recipient, amtOfEXC);\n        return amtOfEXC;\n    }\n\n    /**\n     * @dev Function to check the credit of the owner\n     * @param ad address of the owner\n     * @return uint256 credit of the owner\n     */\n    function checkEXC(address ad) public view returns (uint256) {\n        uint256 credit = erc20Contract.balanceOf(ad);\n        return credit;\n    }\n\n    /**\n     * @dev Function to transfer EXC from the owner to the recipient\n     * @param recipient address of the recipient\n     * @param amt amount of EXC to transfer\n     */\n    function transferEXC(address recipient, uint256 amt) public {\n        // Transfers from tx.origin to receipient\n        erc20Contract.transfer(recipient, amt);\n    }\n\n    /**\n     * @dev Function to transfer EXC from the owner to the recipient\n     * @param sender address of the sender\n     * @param recipient address of the recipient\n     * @param amt amount of EXC to transfer\n     */\n    function transferEXCFrom(\n        address sender,\n        address recipient,\n        uint256 amt\n    ) public {\n        // Transfers from tx.origin to receipient\n        erc20Contract.transferFrom(sender, recipient, amt);\n    }\n\n    /**\n     * @dev Function to destroy EXC\n     * @param tokenOwner address of the owner\n     * @param tokenAmount amount of EXC to destroy\n     * @return uint256 amount of EXC destroyed\n     */\n    function destroyEXC(\n        address tokenOwner,\n        uint256 tokenAmount\n    ) public returns (uint256) {\n        require(\n            checkEXC(tokenOwner) >= tokenAmount,\n            \"Insufficient EXC to burn\"\n        );\n        erc20Contract.burn(tokenOwner, tokenAmount);\n        return tokenAmount;\n    }\n}\n", "sourcePath": "C:\\Users\\<USER>\\Downloads\\EcoXChange\\contracts\\EcoXChangeToken.sol", "ast": {"absolutePath": "project:/contracts/EcoXChangeToken.sol", "exportedSymbols": {"EcoXChangeToken": [4642]}, "id": 4643, "nodeType": "SourceUnit", "nodes": [{"id": 4498, "literals": ["solidity", "^", "0.5", ".0"], "nodeType": "PragmaDirective", "src": "0:23:5"}, {"absolutePath": "project:/contracts/ERC20.sol", "file": "./ERC20.sol", "id": 4499, "nodeType": "ImportDirective", "scope": 4643, "sourceUnit": 3339, "src": "25:21:5", "symbolAliases": [], "unitAlias": ""}, {"baseContracts": [], "contractDependencies": [3338], "contractKind": "contract", "documentation": "@title EcoXChangeToken\n@dev A token contract representing EcoXChange Tokens.", "fullyImplemented": true, "id": 4642, "linearizedBaseContracts": [4642], "name": "EcoXChangeToken", "nodeType": "ContractDefinition", "nodes": [{"anonymous": false, "documentation": null, "id": 4505, "name": "EXCMinted", "nodeType": "EventDefinition", "parameters": {"id": 4504, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4501, "indexed": false, "name": "recipient", "nodeType": "VariableDeclaration", "scope": 4505, "src": "200:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4500, "name": "address", "nodeType": "ElementaryTypeName", "src": "200:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4503, "indexed": false, "name": "amt", "nodeType": "VariableDeclaration", "scope": 4505, "src": "219:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4502, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "219:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "199:32:5"}, "src": "184:48:5"}, {"constant": false, "id": 4507, "name": "erc20Contract", "nodeType": "VariableDeclaration", "scope": 4642, "src": "261:19:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}, "typeName": {"contractScope": null, "id": 4506, "name": "ERC20", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 3338, "src": "261:5:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4511, "name": "owner", "nodeType": "VariableDeclaration", "scope": 4642, "src": "286:26:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4508, "name": "address", "nodeType": "ElementaryTypeName", "src": "286:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 4509, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4946, "src": "302:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 4510, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "302:10:5", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "internal"}, {"body": {"id": 4531, "nodeType": "Block", "src": "366:93:5", "statements": [{"assignments": [4517], "declarations": [{"constant": false, "id": 4517, "name": "e", "nodeType": "VariableDeclaration", "scope": 4531, "src": "376:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}, "typeName": {"contractScope": null, "id": 4516, "name": "ERC20", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 3338, "src": "376:5:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "value": null, "visibility": "internal"}], "id": 4521, "initialValue": {"argumentTypes": null, "arguments": [], "expression": {"argumentTypes": [], "id": 4519, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "NewExpression", "src": "386:9:5", "typeDescriptions": {"typeIdentifier": "t_function_creation_nonpayable$__$returns$_t_contract$_ERC20_$3338_$", "typeString": "function () returns (contract ERC20)"}, "typeName": {"contractScope": null, "id": 4518, "name": "ERC20", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 3338, "src": "390:5:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}}, "id": 4520, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "386:11:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "nodeType": "VariableDeclarationStatement", "src": "376:21:5"}, {"expression": {"argumentTypes": null, "id": 4524, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 4522, "name": "erc20Contract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4507, "src": "407:13:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 4523, "name": "e", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4517, "src": "423:1:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "src": "407:17:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "id": 4525, "nodeType": "ExpressionStatement", "src": "407:17:5"}, {"expression": {"argumentTypes": null, "id": 4529, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 4526, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4511, "src": "434:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 4527, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4946, "src": "442:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 4528, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "442:10:5", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "src": "434:18:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 4530, "nodeType": "ExpressionStatement", "src": "434:18:5"}]}, "documentation": null, "id": 4532, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nodeType": "FunctionDefinition", "parameters": {"id": 4514, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4513, "name": "_erc20TokenAddress", "nodeType": "VariableDeclaration", "scope": 4532, "src": "331:26:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4512, "name": "address", "nodeType": "ElementaryTypeName", "src": "331:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "330:28:5"}, "returnParameters": {"id": 4515, "nodeType": "ParameterList", "parameters": [], "src": "366:0:5"}, "scope": 4642, "src": "319:140:5", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4562, "nodeType": "Block", "src": "823:253:5", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 4544, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 4542, "name": "amtOfEXC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4536, "src": "841:8:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"argumentTypes": null, "hexValue": "30", "id": 4543, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "852:1:5", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "841:12:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "416d6f756e74206d7573742062652067726561746572207468616e207a65726f", "id": 4545, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "855:34:5", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_335ff2e4b249975444723ab3dc1716db90a7dff95cbce35a34ad25055762f887", "typeString": "literal_string \"Amount must be greater than zero\""}, "value": "Amount must be greater than zero"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_335ff2e4b249975444723ab3dc1716db90a7dff95cbce35a34ad25055762f887", "typeString": "literal_string \"Amount must be greater than zero\""}], "id": 4541, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "833:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4546, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "833:57:5", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4547, "nodeType": "ExpressionStatement", "src": "833:57:5"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4551, "name": "recipient", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4534, "src": "979:9:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4552, "name": "amtOfEXC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4536, "src": "990:8:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 4548, "name": "erc20Contract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4507, "src": "960:13:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "id": 4550, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "mint", "nodeType": "MemberAccess", "referencedDeclaration": 3184, "src": "960:18:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 4553, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "960:39:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4554, "nodeType": "ExpressionStatement", "src": "960:39:5"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4556, "name": "recipient", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4534, "src": "1024:9:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4557, "name": "amtOfEXC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4536, "src": "1035:8:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 4555, "name": "EXCMinted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4505, "src": "1014:9:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 4558, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1014:30:5", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4559, "nodeType": "EmitStatement", "src": "1009:35:5"}, {"expression": {"argumentTypes": null, "id": 4560, "name": "amtOfEXC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4536, "src": "1061:8:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 4540, "id": 4561, "nodeType": "Return", "src": "1054:15:5"}]}, "documentation": "@dev Function to mint EcoXChange Tokens (EXC) to the recipient.\n@param recipient Address of the recipient that will receive the EXC.\n@param amtOfEXC Amount of EXC to mint.\n@return The amount of EXC minted.", "id": 4563, "implemented": true, "kind": "function", "modifiers": [], "name": "getEXC", "nodeType": "FunctionDefinition", "parameters": {"id": 4537, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4534, "name": "recipient", "nodeType": "VariableDeclaration", "scope": 4563, "src": "740:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4533, "name": "address", "nodeType": "ElementaryTypeName", "src": "740:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4536, "name": "amtOfEXC", "nodeType": "VariableDeclaration", "scope": 4563, "src": "767:16:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4535, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "767:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "730:59:5"}, "returnParameters": {"id": 4540, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4539, "name": "", "nodeType": "VariableDeclaration", "scope": 4563, "src": "814:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4538, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "814:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "813:9:5"}, "scope": 4642, "src": "715:361:5", "stateMutability": "payable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4579, "nodeType": "Block", "src": "1293:84:5", "statements": [{"assignments": [4571], "declarations": [{"constant": false, "id": 4571, "name": "credit", "nodeType": "VariableDeclaration", "scope": 4579, "src": "1303:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4570, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1303:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "id": 4576, "initialValue": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4574, "name": "ad", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4565, "src": "1344:2:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"argumentTypes": null, "id": 4572, "name": "erc20Contract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4507, "src": "1320:13:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "id": 4573, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "balanceOf", "nodeType": "MemberAccess", "referencedDeclaration": 3076, "src": "1320:23:5", "typeDescriptions": {"typeIdentifier": "t_function_external_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view external returns (uint256)"}}, "id": 4575, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1320:27:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "1303:44:5"}, {"expression": {"argumentTypes": null, "id": 4577, "name": "credit", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4571, "src": "1364:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 4569, "id": 4578, "nodeType": "Return", "src": "1357:13:5"}]}, "documentation": "@dev Function to check the credit of the owner\n@param ad address of the owner\n@return uint256 credit of the owner", "id": 4580, "implemented": true, "kind": "function", "modifiers": [], "name": "checkEXC", "nodeType": "FunctionDefinition", "parameters": {"id": 4566, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4565, "name": "ad", "nodeType": "VariableDeclaration", "scope": 4580, "src": "1251:10:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4564, "name": "address", "nodeType": "ElementaryTypeName", "src": "1251:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "1250:12:5"}, "returnParameters": {"id": 4569, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4568, "name": "", "nodeType": "VariableDeclaration", "scope": 4580, "src": "1284:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4567, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1284:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1283:9:5"}, "scope": 4642, "src": "1233:144:5", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 4594, "nodeType": "Block", "src": "1621:105:5", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4590, "name": "recipient", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4582, "src": "1704:9:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4591, "name": "amt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4584, "src": "1715:3:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 4587, "name": "erc20Contract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4507, "src": "1681:13:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "id": 4589, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": 3137, "src": "1681:22:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 4592, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1681:38:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4593, "nodeType": "ExpressionStatement", "src": "1681:38:5"}]}, "documentation": "@dev Function to transfer EXC from the owner to the recipient\n@param recipient address of the recipient\n@param amt amount of EXC to transfer", "id": 4595, "implemented": true, "kind": "function", "modifiers": [], "name": "transferEXC", "nodeType": "FunctionDefinition", "parameters": {"id": 4585, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4582, "name": "recipient", "nodeType": "VariableDeclaration", "scope": 4595, "src": "1582:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4581, "name": "address", "nodeType": "ElementaryTypeName", "src": "1582:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4584, "name": "amt", "nodeType": "VariableDeclaration", "scope": 4595, "src": "1601:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4583, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1601:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1581:32:5"}, "returnParameters": {"id": 4586, "nodeType": "ParameterList", "parameters": [], "src": "1621:0:5"}, "scope": 4642, "src": "1561:165:5", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4612, "nodeType": "Block", "src": "2063:117:5", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4607, "name": "sender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4597, "src": "2150:6:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4608, "name": "recipient", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4599, "src": "2158:9:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4609, "name": "amt", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4601, "src": "2169:3:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 4604, "name": "erc20Contract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4507, "src": "2123:13:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "id": 4606, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "transferFrom", "nodeType": "MemberAccess", "referencedDeclaration": 3337, "src": "2123:26:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,address,uint256) external returns (bool)"}}, "id": 4610, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2123:50:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4611, "nodeType": "ExpressionStatement", "src": "2123:50:5"}]}, "documentation": "@dev Function to transfer EXC from the owner to the recipient\n@param sender address of the sender\n@param recipient address of the recipient\n@param amt amount of EXC to transfer", "id": 4613, "implemented": true, "kind": "function", "modifiers": [], "name": "transferEXCFrom", "nodeType": "FunctionDefinition", "parameters": {"id": 4602, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4597, "name": "sender", "nodeType": "VariableDeclaration", "scope": 4613, "src": "1987:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4596, "name": "address", "nodeType": "ElementaryTypeName", "src": "1987:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4599, "name": "recipient", "nodeType": "VariableDeclaration", "scope": 4613, "src": "2011:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4598, "name": "address", "nodeType": "ElementaryTypeName", "src": "2011:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4601, "name": "amt", "nodeType": "VariableDeclaration", "scope": 4613, "src": "2038:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4600, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2038:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1977:78:5"}, "returnParameters": {"id": 4603, "nodeType": "ParameterList", "parameters": [], "src": "2063:0:5"}, "scope": 4642, "src": "1953:227:5", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 4640, "nodeType": "Block", "src": "2490:204:5", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 4627, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4624, "name": "tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4615, "src": "2530:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 4623, "name": "checkEXC", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4580, "src": "2521:8:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$_t_address_$returns$_t_uint256_$", "typeString": "function (address) view returns (uint256)"}}, "id": 4625, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2521:20:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 4626, "name": "tokenAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4617, "src": "2545:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2521:35:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "496e73756666696369656e742045584320746f206275726e", "id": 4628, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2570:26:5", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_43491b01c356ecb8dc7923fabcddb580cc538b82450b86bba12c5cf879a71245", "typeString": "literal_string \"Insufficient EXC to burn\""}, "value": "Insufficient EXC to burn"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_43491b01c356ecb8dc7923fabcddb580cc538b82450b86bba12c5cf879a71245", "typeString": "literal_string \"Insufficient EXC to burn\""}], "id": 4622, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "2500:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 4629, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2500:106:5", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 4630, "nodeType": "ExpressionStatement", "src": "2500:106:5"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 4634, "name": "tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4615, "src": "2635:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 4635, "name": "tokenAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4617, "src": "2647:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 4631, "name": "erc20Contract", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4507, "src": "2616:13:5", "typeDescriptions": {"typeIdentifier": "t_contract$_ERC20_$3338", "typeString": "contract ERC20"}}, "id": 4633, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "burn", "nodeType": "MemberAccess", "referencedDeclaration": 3221, "src": "2616:18:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 4636, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2616:43:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 4637, "nodeType": "ExpressionStatement", "src": "2616:43:5"}, {"expression": {"argumentTypes": null, "id": 4638, "name": "tokenAmount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4617, "src": "2676:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 4621, "id": 4639, "nodeType": "Return", "src": "2669:18:5"}]}, "documentation": "@dev Function to destroy EXC\n@param tokenOwner address of the owner\n@param tokenAmount amount of EXC to destroy\n@return uint256 amount of EXC destroyed", "id": 4641, "implemented": true, "kind": "function", "modifiers": [], "name": "destroyEXC", "nodeType": "FunctionDefinition", "parameters": {"id": 4618, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4615, "name": "tokenOwner", "nodeType": "VariableDeclaration", "scope": 4641, "src": "2411:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 4614, "name": "address", "nodeType": "ElementaryTypeName", "src": "2411:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 4617, "name": "tokenAmount", "nodeType": "VariableDeclaration", "scope": 4641, "src": "2439:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4616, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2439:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2401:63:5"}, "returnParameters": {"id": 4621, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 4620, "name": "", "nodeType": "VariableDeclaration", "scope": 4641, "src": "2481:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 4619, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2481:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2480:9:5"}, "scope": 4642, "src": "2382:312:5", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}], "scope": 4643, "src": "139:2557:5"}], "src": "0:2697:5"}, "legacyAST": {"attributes": {"absolutePath": "project:/contracts/EcoXChangeToken.sol", "exportedSymbols": {"EcoXChangeToken": [4642]}}, "children": [{"attributes": {"literals": ["solidity", "^", "0.5", ".0"]}, "id": 4498, "name": "PragmaDirective", "src": "0:23:5"}, {"attributes": {"SourceUnit": 3339, "absolutePath": "project:/contracts/ERC20.sol", "file": "./ERC20.sol", "scope": 4643, "symbolAliases": [null], "unitAlias": ""}, "id": 4499, "name": "ImportDirective", "src": "25:21:5"}, {"attributes": {"baseContracts": [null], "contractDependencies": [3338], "contractKind": "contract", "documentation": "@title EcoXChangeToken\n@dev A token contract representing EcoXChange Tokens.", "fullyImplemented": true, "linearizedBaseContracts": [4642], "name": "EcoXChangeToken", "scope": 4643}, "children": [{"attributes": {"anonymous": false, "documentation": null, "name": "EXCMinted"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": false, "name": "recipient", "scope": 4505, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4500, "name": "ElementaryTypeName", "src": "200:7:5"}], "id": 4501, "name": "VariableDeclaration", "src": "200:17:5"}, {"attributes": {"constant": false, "indexed": false, "name": "amt", "scope": 4505, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4502, "name": "ElementaryTypeName", "src": "219:7:5"}], "id": 4503, "name": "VariableDeclaration", "src": "219:11:5"}], "id": 4504, "name": "ParameterList", "src": "199:32:5"}], "id": 4505, "name": "EventDefinition", "src": "184:48:5"}, {"attributes": {"constant": false, "name": "erc20Contract", "scope": 4642, "stateVariable": true, "storageLocation": "default", "type": "contract ERC20", "value": null, "visibility": "internal"}, "children": [{"attributes": {"contractScope": null, "name": "ERC20", "referencedDeclaration": 3338, "type": "contract ERC20"}, "id": 4506, "name": "UserDefinedTypeName", "src": "261:5:5"}], "id": 4507, "name": "VariableDeclaration", "src": "261:19:5"}, {"attributes": {"constant": false, "name": "owner", "scope": 4642, "stateVariable": true, "storageLocation": "default", "type": "address", "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4508, "name": "ElementaryTypeName", "src": "286:7:5"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4946, "type": "msg", "value": "msg"}, "id": 4509, "name": "Identifier", "src": "302:3:5"}], "id": 4510, "name": "MemberAccess", "src": "302:10:5"}], "id": 4511, "name": "VariableDeclaration", "src": "286:26:5"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": true, "kind": "constructor", "modifiers": [null], "name": "", "scope": 4642, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_erc20TokenAddress", "scope": 4532, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4512, "name": "ElementaryTypeName", "src": "331:7:5"}], "id": 4513, "name": "VariableDeclaration", "src": "331:26:5"}], "id": 4514, "name": "ParameterList", "src": "330:28:5"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4515, "name": "ParameterList", "src": "366:0:5"}, {"children": [{"attributes": {"assignments": [4517]}, "children": [{"attributes": {"constant": false, "name": "e", "scope": 4531, "stateVariable": false, "storageLocation": "default", "type": "contract ERC20", "value": null, "visibility": "internal"}, "children": [{"attributes": {"contractScope": null, "name": "ERC20", "referencedDeclaration": 3338, "type": "contract ERC20"}, "id": 4516, "name": "UserDefinedTypeName", "src": "376:5:5"}], "id": 4517, "name": "VariableDeclaration", "src": "376:7:5"}, {"attributes": {"argumentTypes": null, "arguments": [null], "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "contract ERC20", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [null], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "type": "function () returns (contract ERC20)"}, "children": [{"attributes": {"contractScope": null, "name": "ERC20", "referencedDeclaration": 3338, "type": "contract ERC20"}, "id": 4518, "name": "UserDefinedTypeName", "src": "390:5:5"}], "id": 4519, "name": "NewExpression", "src": "386:9:5"}], "id": 4520, "name": "FunctionCall", "src": "386:11:5"}], "id": 4521, "name": "VariableDeclarationStatement", "src": "376:21:5"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "contract ERC20"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4507, "type": "contract ERC20", "value": "erc20Contract"}, "id": 4522, "name": "Identifier", "src": "407:13:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4517, "type": "contract ERC20", "value": "e"}, "id": 4523, "name": "Identifier", "src": "423:1:5"}], "id": 4524, "name": "Assignment", "src": "407:17:5"}], "id": 4525, "name": "ExpressionStatement", "src": "407:17:5"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "address"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4511, "type": "address", "value": "owner"}, "id": 4526, "name": "Identifier", "src": "434:5:5"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4946, "type": "msg", "value": "msg"}, "id": 4527, "name": "Identifier", "src": "442:3:5"}], "id": 4528, "name": "MemberAccess", "src": "442:10:5"}], "id": 4529, "name": "Assignment", "src": "434:18:5"}], "id": 4530, "name": "ExpressionStatement", "src": "434:18:5"}], "id": 4531, "name": "Block", "src": "366:93:5"}], "id": 4532, "name": "FunctionDefinition", "src": "319:140:5"}, {"attributes": {"documentation": "@dev Function to mint EcoXChange Tokens (EXC) to the recipient.\n@param recipient Address of the recipient that will receive the EXC.\n@param amtOfEXC Amount of EXC to mint.\n@return The amount of EXC minted.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getEXC", "scope": 4642, "stateMutability": "payable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "recipient", "scope": 4563, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4533, "name": "ElementaryTypeName", "src": "740:7:5"}], "id": 4534, "name": "VariableDeclaration", "src": "740:17:5"}, {"attributes": {"constant": false, "name": "amtOfEXC", "scope": 4563, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4535, "name": "ElementaryTypeName", "src": "767:7:5"}], "id": 4536, "name": "VariableDeclaration", "src": "767:16:5"}], "id": 4537, "name": "ParameterList", "src": "730:59:5"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 4563, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4538, "name": "ElementaryTypeName", "src": "814:7:5"}], "id": 4539, "name": "VariableDeclaration", "src": "814:7:5"}], "id": 4540, "name": "ParameterList", "src": "813:9:5"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_335ff2e4b249975444723ab3dc1716db90a7dff95cbce35a34ad25055762f887", "typeString": "literal_string \"Amount must be greater than zero\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4541, "name": "Identifier", "src": "833:7:5"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": ">", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4536, "type": "uint256", "value": "amtOfEXC"}, "id": 4542, "name": "Identifier", "src": "841:8:5"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 4543, "name": "Literal", "src": "852:1:5"}], "id": 4544, "name": "BinaryOperation", "src": "841:12:5"}, {"attributes": {"argumentTypes": null, "hexvalue": "416d6f756e74206d7573742062652067726561746572207468616e207a65726f", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Amount must be greater than zero\"", "value": "Amount must be greater than zero"}, "id": 4545, "name": "Literal", "src": "855:34:5"}], "id": 4546, "name": "FunctionCall", "src": "833:57:5"}], "id": 4547, "name": "ExpressionStatement", "src": "833:57:5"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "bool", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "mint", "referencedDeclaration": 3184, "type": "function (address,uint256) external returns (bool)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4507, "type": "contract ERC20", "value": "erc20Contract"}, "id": 4548, "name": "Identifier", "src": "960:13:5"}], "id": 4550, "name": "MemberAccess", "src": "960:18:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4534, "type": "address", "value": "recipient"}, "id": 4551, "name": "Identifier", "src": "979:9:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4536, "type": "uint256", "value": "amtOfEXC"}, "id": 4552, "name": "Identifier", "src": "990:8:5"}], "id": 4553, "name": "FunctionCall", "src": "960:39:5"}], "id": 4554, "name": "ExpressionStatement", "src": "960:39:5"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 4505, "type": "function (address,uint256)", "value": "EXCMinted"}, "id": 4555, "name": "Identifier", "src": "1014:9:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4534, "type": "address", "value": "recipient"}, "id": 4556, "name": "Identifier", "src": "1024:9:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4536, "type": "uint256", "value": "amtOfEXC"}, "id": 4557, "name": "Identifier", "src": "1035:8:5"}], "id": 4558, "name": "FunctionCall", "src": "1014:30:5"}], "id": 4559, "name": "EmitStatement", "src": "1009:35:5"}, {"attributes": {"functionReturnParameters": 4540}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4536, "type": "uint256", "value": "amtOfEXC"}, "id": 4560, "name": "Identifier", "src": "1061:8:5"}], "id": 4561, "name": "Return", "src": "1054:15:5"}], "id": 4562, "name": "Block", "src": "823:253:5"}], "id": 4563, "name": "FunctionDefinition", "src": "715:361:5"}, {"attributes": {"documentation": "@dev Function to check the credit of the owner\n@param ad address of the owner\n@return uint256 credit of the owner", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "checkEXC", "scope": 4642, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "ad", "scope": 4580, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4564, "name": "ElementaryTypeName", "src": "1251:7:5"}], "id": 4565, "name": "VariableDeclaration", "src": "1251:10:5"}], "id": 4566, "name": "ParameterList", "src": "1250:12:5"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 4580, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4567, "name": "ElementaryTypeName", "src": "1284:7:5"}], "id": 4568, "name": "VariableDeclaration", "src": "1284:7:5"}], "id": 4569, "name": "ParameterList", "src": "1283:9:5"}, {"children": [{"attributes": {"assignments": [4571]}, "children": [{"attributes": {"constant": false, "name": "credit", "scope": 4579, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4570, "name": "ElementaryTypeName", "src": "1303:7:5"}], "id": 4571, "name": "VariableDeclaration", "src": "1303:14:5"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "balanceOf", "referencedDeclaration": 3076, "type": "function (address) view external returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4507, "type": "contract ERC20", "value": "erc20Contract"}, "id": 4572, "name": "Identifier", "src": "1320:13:5"}], "id": 4573, "name": "MemberAccess", "src": "1320:23:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4565, "type": "address", "value": "ad"}, "id": 4574, "name": "Identifier", "src": "1344:2:5"}], "id": 4575, "name": "FunctionCall", "src": "1320:27:5"}], "id": 4576, "name": "VariableDeclarationStatement", "src": "1303:44:5"}, {"attributes": {"functionReturnParameters": 4569}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4571, "type": "uint256", "value": "credit"}, "id": 4577, "name": "Identifier", "src": "1364:6:5"}], "id": 4578, "name": "Return", "src": "1357:13:5"}], "id": 4579, "name": "Block", "src": "1293:84:5"}], "id": 4580, "name": "FunctionDefinition", "src": "1233:144:5"}, {"attributes": {"documentation": "@dev Function to transfer EXC from the owner to the recipient\n@param recipient address of the recipient\n@param amt amount of EXC to transfer", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "transferEXC", "scope": 4642, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "recipient", "scope": 4595, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4581, "name": "ElementaryTypeName", "src": "1582:7:5"}], "id": 4582, "name": "VariableDeclaration", "src": "1582:17:5"}, {"attributes": {"constant": false, "name": "amt", "scope": 4595, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4583, "name": "ElementaryTypeName", "src": "1601:7:5"}], "id": 4584, "name": "VariableDeclaration", "src": "1601:11:5"}], "id": 4585, "name": "ParameterList", "src": "1581:32:5"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4586, "name": "ParameterList", "src": "1621:0:5"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "bool", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "transfer", "referencedDeclaration": 3137, "type": "function (address,uint256) external returns (bool)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4507, "type": "contract ERC20", "value": "erc20Contract"}, "id": 4587, "name": "Identifier", "src": "1681:13:5"}], "id": 4589, "name": "MemberAccess", "src": "1681:22:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4582, "type": "address", "value": "recipient"}, "id": 4590, "name": "Identifier", "src": "1704:9:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4584, "type": "uint256", "value": "amt"}, "id": 4591, "name": "Identifier", "src": "1715:3:5"}], "id": 4592, "name": "FunctionCall", "src": "1681:38:5"}], "id": 4593, "name": "ExpressionStatement", "src": "1681:38:5"}], "id": 4594, "name": "Block", "src": "1621:105:5"}], "id": 4595, "name": "FunctionDefinition", "src": "1561:165:5"}, {"attributes": {"documentation": "@dev Function to transfer EXC from the owner to the recipient\n@param sender address of the sender\n@param recipient address of the recipient\n@param amt amount of EXC to transfer", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "transferEXCFrom", "scope": 4642, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "sender", "scope": 4613, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4596, "name": "ElementaryTypeName", "src": "1987:7:5"}], "id": 4597, "name": "VariableDeclaration", "src": "1987:14:5"}, {"attributes": {"constant": false, "name": "recipient", "scope": 4613, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4598, "name": "ElementaryTypeName", "src": "2011:7:5"}], "id": 4599, "name": "VariableDeclaration", "src": "2011:17:5"}, {"attributes": {"constant": false, "name": "amt", "scope": 4613, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4600, "name": "ElementaryTypeName", "src": "2038:7:5"}], "id": 4601, "name": "VariableDeclaration", "src": "2038:11:5"}], "id": 4602, "name": "ParameterList", "src": "1977:78:5"}, {"attributes": {"parameters": [null]}, "children": [], "id": 4603, "name": "ParameterList", "src": "2063:0:5"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "bool", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "transferFrom", "referencedDeclaration": 3337, "type": "function (address,address,uint256) external returns (bool)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4507, "type": "contract ERC20", "value": "erc20Contract"}, "id": 4604, "name": "Identifier", "src": "2123:13:5"}], "id": 4606, "name": "MemberAccess", "src": "2123:26:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4597, "type": "address", "value": "sender"}, "id": 4607, "name": "Identifier", "src": "2150:6:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4599, "type": "address", "value": "recipient"}, "id": 4608, "name": "Identifier", "src": "2158:9:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4601, "type": "uint256", "value": "amt"}, "id": 4609, "name": "Identifier", "src": "2169:3:5"}], "id": 4610, "name": "FunctionCall", "src": "2123:50:5"}], "id": 4611, "name": "ExpressionStatement", "src": "2123:50:5"}], "id": 4612, "name": "Block", "src": "2063:117:5"}], "id": 4613, "name": "FunctionDefinition", "src": "1953:227:5"}, {"attributes": {"documentation": "@dev Function to destroy EXC\n@param tokenOwner address of the owner\n@param tokenAmount amount of EXC to destroy\n@return uint256 amount of EXC destroyed", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "destroyEXC", "scope": 4642, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "tokenOwner", "scope": 4641, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 4614, "name": "ElementaryTypeName", "src": "2411:7:5"}], "id": 4615, "name": "VariableDeclaration", "src": "2411:18:5"}, {"attributes": {"constant": false, "name": "tokenAmount", "scope": 4641, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4616, "name": "ElementaryTypeName", "src": "2439:7:5"}], "id": 4617, "name": "VariableDeclaration", "src": "2439:19:5"}], "id": 4618, "name": "ParameterList", "src": "2401:63:5"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 4641, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 4619, "name": "ElementaryTypeName", "src": "2481:7:5"}], "id": 4620, "name": "VariableDeclaration", "src": "2481:7:5"}], "id": 4621, "name": "ParameterList", "src": "2480:9:5"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_43491b01c356ecb8dc7923fabcddb580cc538b82450b86bba12c5cf879a71245", "typeString": "literal_string \"Insufficient EXC to burn\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 4622, "name": "Identifier", "src": "2500:7:5"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": ">=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 4580, "type": "function (address) view returns (uint256)", "value": "checkEXC"}, "id": 4623, "name": "Identifier", "src": "2521:8:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4615, "type": "address", "value": "tokenOwner"}, "id": 4624, "name": "Identifier", "src": "2530:10:5"}], "id": 4625, "name": "FunctionCall", "src": "2521:20:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4617, "type": "uint256", "value": "tokenAmount"}, "id": 4626, "name": "Identifier", "src": "2545:11:5"}], "id": 4627, "name": "BinaryOperation", "src": "2521:35:5"}, {"attributes": {"argumentTypes": null, "hexvalue": "496e73756666696369656e742045584320746f206275726e", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Insufficient EXC to burn\"", "value": "Insufficient EXC to burn"}, "id": 4628, "name": "Literal", "src": "2570:26:5"}], "id": 4629, "name": "FunctionCall", "src": "2500:106:5"}], "id": 4630, "name": "ExpressionStatement", "src": "2500:106:5"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "bool", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "burn", "referencedDeclaration": 3221, "type": "function (address,uint256) external returns (bool)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4507, "type": "contract ERC20", "value": "erc20Contract"}, "id": 4631, "name": "Identifier", "src": "2616:13:5"}], "id": 4633, "name": "MemberAccess", "src": "2616:18:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4615, "type": "address", "value": "tokenOwner"}, "id": 4634, "name": "Identifier", "src": "2635:10:5"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4617, "type": "uint256", "value": "tokenAmount"}, "id": 4635, "name": "Identifier", "src": "2647:11:5"}], "id": 4636, "name": "FunctionCall", "src": "2616:43:5"}], "id": 4637, "name": "ExpressionStatement", "src": "2616:43:5"}, {"attributes": {"functionReturnParameters": 4621}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4617, "type": "uint256", "value": "tokenAmount"}, "id": 4638, "name": "Identifier", "src": "2676:11:5"}], "id": 4639, "name": "Return", "src": "2669:18:5"}], "id": 4640, "name": "Block", "src": "2490:204:5"}], "id": 4641, "name": "FunctionDefinition", "src": "2382:312:5"}], "id": 4642, "name": "ContractDefinition", "src": "139:2557:5"}], "id": 4643, "name": "SourceUnit", "src": "0:2697:5"}, "compiler": {"name": "solc", "version": "0.5.17+commit.d19bba13.Emscripten.clang"}, "networks": {}, "schemaVersion": "3.4.16", "updatedAt": "2025-06-23T09:15:20.628Z", "devdoc": {"details": "A token contract representing EcoXChange Tokens.", "methods": {"checkEXC(address)": {"details": "Function to check the credit of the owner", "params": {"ad": "address of the owner"}, "return": "uint256 credit of the owner"}, "destroyEXC(address,uint256)": {"details": "Function to destroy EXC", "params": {"tokenAmount": "amount of EXC to destroy", "tokenOwner": "address of the owner"}, "return": "uint256 amount of EXC destroyed"}, "getEXC(address,uint256)": {"details": "Function to mint EcoXChange Tokens (EXC) to the recipient.", "params": {"amtOfEXC": "Amount of EXC to mint.", "recipient": "Address of the recipient that will receive the EXC."}, "return": "The amount of EXC minted."}, "transferEXC(address,uint256)": {"details": "Function to transfer EXC from the owner to the recipient", "params": {"amt": "amount of EXC to transfer", "recipient": "address of the recipient"}}, "transferEXCFrom(address,address,uint256)": {"details": "Function to transfer EXC from the owner to the recipient", "params": {"amt": "amount of EXC to transfer", "recipient": "address of the recipient", "sender": "address of the sender"}}}, "title": "EcoXChangeToken"}, "userdoc": {"methods": {}}}