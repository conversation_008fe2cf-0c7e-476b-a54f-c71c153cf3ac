{"contractName": "SafeMath", "abi": [], "metadata": "{\"compiler\":{\"version\":\"0.5.17+commit.d19bba13\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Math operations with safety checks that throw on error\",\"methods\":{},\"title\":\"SafeMath\"},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"project:/contracts/ERC20.sol\":\"SafeMath\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/ERC20.sol\":{\"keccak256\":\"0x8cbb2a43af37209caeb39903b2e45a7d7276f102ad83283a7eb118cb3fa7d591\",\"urls\":[\"bzz-raw://9e349590ff1f0b362f812af1017e5ff3d23758e20d55780a3ea03d9deab8cee6\",\"dweb:/ipfs/QmQkBgdLNCHCd7DZ4ToUTAMxFPi7pz9hxG2TMG2pKfAiPi\"]}},\"version\":1}", "bytecode": "0x60556023600b82828239805160001a607314601657fe5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea265627a7a72315820dfd1041b28cbf218eb00e98063b8bc90984aa8d609a0a9ac6bb8a12b7b33daea64736f6c63430005110032", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea265627a7a72315820dfd1041b28cbf218eb00e98063b8bc90984aa8d609a0a9ac6bb8a12b7b33daea64736f6c63430005110032", "sourceMap": "241:1125:3:-;;132:2:-1;166:7;155:9;146:7;137:37;255:7;249:14;246:1;241:23;235:4;232:33;222:2;;269:9;222:2;293:9;290:1;283:20;323:4;314:7;306:22;347:7;338;331:24", "deployedSourceMap": "241:1125:3:-;;;;;;;;", "source": "pragma solidity ^0.5.0;\n\n//first need to approve the address of spender\n// Check the allowance\n//Finally able to call transferFrom to transfer tokens\n\n/**\n * @title SafeMath\n * @dev Math operations with safety checks that throw on error\n */\nlibrary SafeMath {\n    /**\n     * @dev Multiplies two numbers, throws on overflow.\n     */\n    function mul(uint256 a, uint256 b) internal pure returns (uint256 c) {\n        if (a == 0) {\n            return 0;\n        }\n        c = a * b;\n        assert(c / a == b);\n        return c;\n    }\n\n    /**\n     * @dev Integer division of two numbers, truncating the quotient.\n     */\n    function div(uint256 a, uint256 b) internal pure returns (uint256) {\n        // assert(b > 0); // Solidity automatically throws when dividing by 0\n        // uint256 c = a / b;\n        // assert(a == b * c + a % b); // There is no case in which this doesn't hold\n        return a / b;\n    }\n\n    /**\n     * @dev Subtracts two numbers, throws on overflow (i.e. if subtrahend is greater than minuend).\n     */\n    function sub(uint256 a, uint256 b) internal pure returns (uint256) {\n        assert(b <= a);\n        return a - b;\n    }\n\n    /**\n     * @dev Adds two numbers, throws on overflow.\n     */\n    function add(uint256 a, uint256 b) internal pure returns (uint256 c) {\n        c = a + b;\n        assert(c >= a);\n        return c;\n    }\n}\n\ncontract ERC20 {\n    using SafeMath for uint256;\n\n    bool public mintingFinished = false;\n\n    address public owner = msg.sender;\n\n    mapping(address => mapping(address => uint256)) internal allowed;\n    mapping(address => uint256) balances;\n\n    string public constant name = \"EcoXChange Token\";\n    string public constant symbol = \"EXC\";\n    uint8 public constant decimals = 18; //idk what this is\n    uint256 totalSupply_;\n\n    event Transfer(address indexed from, address indexed to, uint256 value);\n    event Approval(\n        address indexed owner,\n        address indexed spender,\n        uint256 value\n    );\n    event Burn(address indexed from, uint256 amount);\n    event Mint(address indexed to, uint256 amount);\n    event MintFinished();\n\n    /**\n     * @dev Gets the balance of the specified address.\n     * @param _owner The address to query the the balance of.\n     * @return An uint256 representing the amount owned by the passed address.\n     */\n    function balanceOf(address _owner) public view returns (uint256) {\n        return balances[_owner];\n    }\n\n    /**\n     * @dev transfer token for a specified address\n     * @param _to The address to transfer to.\n     * @param _value The amount to be transferred.\n     */\n    function transfer(address _to, uint256 _value) public returns (bool) {\n        require(_to != address(0));\n        require(\n            _value <= balances[tx.origin],\n            \"msg.sender doesn't have enough balance\"\n        );\n\n        balances[tx.origin] = balances[tx.origin].sub(_value);\n        balances[_to] = balances[_to].add(_value);\n        emit Transfer(tx.origin, _to, _value);\n        return true;\n    }\n\n    /**\n     * @dev Function to mint tokens\n     * @param _to The address that will receive the minted tokens.\n     * @param _amount The amount of tokens to mint.\n     * @return A boolean that indicates if the operation was successful.\n     */\n    function mint(\n        address _to,\n        uint256 _amount\n    ) public onlyOwner canMint returns (bool) {\n        totalSupply_ = totalSupply_.add(_amount);\n        balances[_to] = balances[_to].add(_amount);\n        emit Mint(_to, _amount);\n        emit Transfer(address(0), _to, _amount);\n        return true;\n    }\n\n    /**\n     * @dev Function to burn tokens\n     */\n    function burn(\n        address _tokenOwner,\n        uint256 _amount\n    ) public onlyOwner returns (bool) {\n        totalSupply_ = totalSupply_.sub(_amount);\n        balances[_tokenOwner] = balances[_tokenOwner].sub(_amount);\n        emit Burn(_tokenOwner, _amount);\n        return true;\n    }\n\n    function getOwner() public view returns (address) {\n        return owner;\n    }\n\n    modifier onlyOwner() {\n        require(msg.sender == owner);\n        _;\n    }\n\n    modifier canMint() {\n        require(!mintingFinished);\n        _;\n    }\n\n    // /**\n    //  * @dev Function to stop minting new tokens.\n    //  * @return True if the operation was successful.\n    //  */\n    // function finishMinting() onlyOwner canMint public returns (bool) {\n    //   mintingFinished = true;\n    //   emit MintFinished();\n    //   return true;\n    // }\n\n    /**\n     * @dev Transfer tokens from one address to another\n     * @param _from address The address which you want to send tokens from\n     * @param _to address The address which you want to transfer to\n     * @param _value uint256 the amount of tokens to be transferred\n     */\n    function transferFrom(\n        address _from,\n        address _to,\n        uint256 _value\n    ) public returns (bool) {\n        require(_to != address(0));\n        require(_value <= balances[_from], \"From doesn't have enough balance\");\n        require(\n            _value <= allowed[_from][tx.origin],\n            \"Not allowed to spend this much\"\n        );\n\n        balances[_from] = balances[_from].sub(_value);\n        balances[_to] = balances[_to].add(_value);\n        allowed[_from][tx.origin] = allowed[_from][tx.origin].sub(_value);\n        emit Transfer(_from, _to, _value);\n        return true;\n    }\n\n    // /**\n    //  * @dev Approve the passed address to spend the specified amount of tokens on behalf of msg.sender.\n    //  *\n    //  * Beware that changing an allowance with this method brings the risk that someone may use both the old\n    //  * and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this\n    //  * race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards:\n    //  * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\n    //  * @param _spender The address which will spend the funds.\n    //  * @param _value The amount of tokens to be spent.\n    //  */\n    // function approve(address _spender, uint256 _value) public returns (bool) {\n    //   allowed[msg.sender][_spender] = _value;\n    //   emit Approval(msg.sender, _spender, _value);\n    //   return true;\n    // }\n\n    // /**\n    //  * @dev Function to check the amount of tokens that an owner allowed to a spender.\n    //  * @param _owner address The address which owns the funds.\n    //  * @param _spender address The address which will spend the funds.\n    //  * @return A uint256 specifying the amount of tokens still available for the spender.\n    //  */\n    // function allowance(address _owner, address _spender) public view returns (uint256) {\n    //   return allowed[_owner][_spender];\n    // }\n\n    // /**\n    // * @dev total number of tokens in existence\n    // */\n    // function totalSupply() public view returns (uint256) {\n    //   return totalSupply_;\n    // }\n}\n", "sourcePath": "C:\\Users\\<USER>\\Downloads\\EcoXChange\\contracts\\ERC20.sol", "ast": {"absolutePath": "project:/contracts/ERC20.sol", "exportedSymbols": {"ERC20": [3338], "SafeMath": [3003]}, "id": 3339, "nodeType": "SourceUnit", "nodes": [{"id": 2911, "literals": ["solidity", "^", "0.5", ".0"], "nodeType": "PragmaDirective", "src": "0:23:3"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "library", "documentation": "@title SafeMath\n@dev Math operations with safety checks that throw on error", "fullyImplemented": true, "id": 3003, "linearizedBaseContracts": [3003], "name": "SafeMath", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 2943, "nodeType": "Block", "src": "405:126:3", "statements": [{"condition": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2922, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2920, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2913, "src": "419:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"argumentTypes": null, "hexValue": "30", "id": 2921, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "424:1:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "419:6:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": null, "id": 2926, "nodeType": "IfStatement", "src": "415:45:3", "trueBody": {"id": 2925, "nodeType": "Block", "src": "427:33:3", "statements": [{"expression": {"argumentTypes": null, "hexValue": "30", "id": 2923, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "448:1:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 2919, "id": 2924, "nodeType": "Return", "src": "441:8:3"}]}}, {"expression": {"argumentTypes": null, "id": 2931, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 2927, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2918, "src": "469:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2930, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2928, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2913, "src": "473:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"argumentTypes": null, "id": 2929, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2915, "src": "477:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "473:5:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "469:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 2932, "nodeType": "ExpressionStatement", "src": "469:9:3"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2938, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2936, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2934, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2918, "src": "495:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"argumentTypes": null, "id": 2935, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2913, "src": "499:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "495:5:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"argumentTypes": null, "id": 2937, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2915, "src": "504:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "495:10:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 2933, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4935, "src": "488:6:3", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 2939, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "488:18:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 2940, "nodeType": "ExpressionStatement", "src": "488:18:3"}, {"expression": {"argumentTypes": null, "id": 2941, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2918, "src": "523:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 2919, "id": 2942, "nodeType": "Return", "src": "516:8:3"}]}, "documentation": "@dev Multiplies two numbers, throws on overflow.", "id": 2944, "implemented": true, "kind": "function", "modifiers": [], "name": "mul", "nodeType": "FunctionDefinition", "parameters": {"id": 2916, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2913, "name": "a", "nodeType": "VariableDeclaration", "scope": 2944, "src": "349:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2912, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "349:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 2915, "name": "b", "nodeType": "VariableDeclaration", "scope": 2944, "src": "360:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2914, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "360:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "348:22:3"}, "returnParameters": {"id": 2919, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2918, "name": "c", "nodeType": "VariableDeclaration", "scope": 2944, "src": "394:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2917, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "394:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "393:11:3"}, "scope": 3003, "src": "336:195:3", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, {"body": {"id": 2957, "nodeType": "Block", "src": "690:223:3", "statements": [{"expression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2955, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2953, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2946, "src": "901:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "/", "rightExpression": {"argumentTypes": null, "id": 2954, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2948, "src": "905:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "901:5:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 2952, "id": 2956, "nodeType": "Return", "src": "894:12:3"}]}, "documentation": "@dev Integer division of two numbers, truncating the quotient.", "id": 2958, "implemented": true, "kind": "function", "modifiers": [], "name": "div", "nodeType": "FunctionDefinition", "parameters": {"id": 2949, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2946, "name": "a", "nodeType": "VariableDeclaration", "scope": 2958, "src": "636:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2945, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "636:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 2948, "name": "b", "nodeType": "VariableDeclaration", "scope": 2958, "src": "647:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2947, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "647:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "635:22:3"}, "returnParameters": {"id": 2952, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2951, "name": "", "nodeType": "VariableDeclaration", "scope": 2958, "src": "681:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2950, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "681:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "680:9:3"}, "scope": 3003, "src": "623:290:3", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, {"body": {"id": 2977, "nodeType": "Block", "src": "1102:53:3", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2970, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2968, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2962, "src": "1119:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "id": 2969, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2960, "src": "1124:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1119:6:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 2967, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4935, "src": "1112:6:3", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 2971, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1112:14:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 2972, "nodeType": "ExpressionStatement", "src": "1112:14:3"}, {"expression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2975, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2973, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2960, "src": "1143:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"argumentTypes": null, "id": 2974, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2962, "src": "1147:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1143:5:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 2966, "id": 2976, "nodeType": "Return", "src": "1136:12:3"}]}, "documentation": "@dev Subtracts two numbers, throws on overflow (i.e. if subtrahend is greater than minuend).", "id": 2978, "implemented": true, "kind": "function", "modifiers": [], "name": "sub", "nodeType": "FunctionDefinition", "parameters": {"id": 2963, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2960, "name": "a", "nodeType": "VariableDeclaration", "scope": 2978, "src": "1048:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2959, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1048:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 2962, "name": "b", "nodeType": "VariableDeclaration", "scope": 2978, "src": "1059:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2961, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1059:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1047:22:3"}, "returnParameters": {"id": 2966, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2965, "name": "", "nodeType": "VariableDeclaration", "scope": 2978, "src": "1093:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2964, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1093:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1092:9:3"}, "scope": 3003, "src": "1035:120:3", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, {"body": {"id": 3001, "nodeType": "Block", "src": "1296:68:3", "statements": [{"expression": {"argumentTypes": null, "id": 2991, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 2987, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2985, "src": "1306:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2990, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2988, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2980, "src": "1310:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"argumentTypes": null, "id": 2989, "name": "b", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2982, "src": "1314:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1310:5:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1306:9:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 2992, "nodeType": "ExpressionStatement", "src": "1306:9:3"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 2996, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 2994, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2985, "src": "1332:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"argumentTypes": null, "id": 2995, "name": "a", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2980, "src": "1337:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "1332:6:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 2993, "name": "assert", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4935, "src": "1325:6:3", "typeDescriptions": {"typeIdentifier": "t_function_assert_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 2997, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1325:14:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 2998, "nodeType": "ExpressionStatement", "src": "1325:14:3"}, {"expression": {"argumentTypes": null, "id": 2999, "name": "c", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 2985, "src": "1356:1:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 2986, "id": 3000, "nodeType": "Return", "src": "1349:8:3"}]}, "documentation": "@dev Adds two numbers, throws on overflow.", "id": 3002, "implemented": true, "kind": "function", "modifiers": [], "name": "add", "nodeType": "FunctionDefinition", "parameters": {"id": 2983, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2980, "name": "a", "nodeType": "VariableDeclaration", "scope": 3002, "src": "1240:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2979, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1240:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 2982, "name": "b", "nodeType": "VariableDeclaration", "scope": 3002, "src": "1251:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2981, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1251:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1239:22:3"}, "returnParameters": {"id": 2986, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 2985, "name": "c", "nodeType": "VariableDeclaration", "scope": 3002, "src": "1285:9:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 2984, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1285:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1284:11:3"}, "scope": 3003, "src": "1227:137:3", "stateMutability": "pure", "superFunction": null, "visibility": "internal"}], "scope": 3339, "src": "241:1125:3"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 3338, "linearizedBaseContracts": [3338], "name": "ERC20", "nodeType": "ContractDefinition", "nodes": [{"id": 3006, "libraryName": {"contractScope": null, "id": 3004, "name": "SafeMath", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 3003, "src": "1395:8:3", "typeDescriptions": {"typeIdentifier": "t_contract$_SafeMath_$3003", "typeString": "library SafeMath"}}, "nodeType": "UsingForDirective", "src": "1389:27:3", "typeName": {"id": 3005, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1408:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, {"constant": false, "id": 3009, "name": "mintingFinished", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1422:35:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3007, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1422:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": {"argumentTypes": null, "hexValue": "66616c7365", "id": 3008, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "1452:5:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "visibility": "public"}, {"constant": false, "id": 3013, "name": "owner", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1464:33:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3010, "name": "address", "nodeType": "ElementaryTypeName", "src": "1464:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3011, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4946, "src": "1487:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 3012, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1487:10:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "visibility": "public"}, {"constant": false, "id": 3019, "name": "allowed", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1504:64:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "typeName": {"id": 3018, "keyType": {"id": 3014, "name": "address", "nodeType": "ElementaryTypeName", "src": "1512:7:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1504:47:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}, "valueType": {"id": 3017, "keyType": {"id": 3015, "name": "address", "nodeType": "ElementaryTypeName", "src": "1531:7:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1523:27:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 3016, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1542:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3023, "name": "balances", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1574:36:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "typeName": {"id": 3022, "keyType": {"id": 3020, "name": "address", "nodeType": "ElementaryTypeName", "src": "1582:7:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1574:27:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}, "valueType": {"id": 3021, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1593:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}}, "value": null, "visibility": "internal"}, {"constant": true, "id": 3026, "name": "name", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1617:48:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory", "typeString": "string"}, "typeName": {"id": 3024, "name": "string", "nodeType": "ElementaryTypeName", "src": "1617:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "45636f584368616e676520546f6b656e", "id": 3025, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1647:18:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_6700cbdcebe7f8ca6179d856f69808a62b2421476dd070ef8a13dfdddef6df9c", "typeString": "literal_string \"EcoXChange Token\""}, "value": "EcoXChange Token"}, "visibility": "public"}, {"constant": true, "id": 3029, "name": "symbol", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1671:37:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory", "typeString": "string"}, "typeName": {"id": 3027, "name": "string", "nodeType": "ElementaryTypeName", "src": "1671:6:3", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": {"argumentTypes": null, "hexValue": "455843", "id": 3028, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1703:5:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_771d787df2a76d8f7504a4312bf21a59db6dbd3d6e4e3b389659445395c4a4a4", "typeString": "literal_string \"EXC\""}, "value": "EXC"}, "visibility": "public"}, {"constant": true, "id": 3032, "name": "decimals", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1714:35:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}, "typeName": {"id": 3030, "name": "uint8", "nodeType": "ElementaryTypeName", "src": "1714:5:3", "typeDescriptions": {"typeIdentifier": "t_uint8", "typeString": "uint8"}}, "value": {"argumentTypes": null, "hexValue": "3138", "id": 3031, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1747:2:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "visibility": "public"}, {"constant": false, "id": 3034, "name": "totalSupply_", "nodeType": "VariableDeclaration", "scope": 3338, "src": "1774:20:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3033, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1774:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"anonymous": false, "documentation": null, "id": 3042, "name": "Transfer", "nodeType": "EventDefinition", "parameters": {"id": 3041, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3036, "indexed": true, "name": "from", "nodeType": "VariableDeclaration", "scope": 3042, "src": "1816:20:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3035, "name": "address", "nodeType": "ElementaryTypeName", "src": "1816:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3038, "indexed": true, "name": "to", "nodeType": "VariableDeclaration", "scope": 3042, "src": "1838:18:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3037, "name": "address", "nodeType": "ElementaryTypeName", "src": "1838:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3040, "indexed": false, "name": "value", "nodeType": "VariableDeclaration", "scope": 3042, "src": "1858:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3039, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1858:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1815:57:3"}, "src": "1801:72:3"}, {"anonymous": false, "documentation": null, "id": 3050, "name": "Approval", "nodeType": "EventDefinition", "parameters": {"id": 3049, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3044, "indexed": true, "name": "owner", "nodeType": "VariableDeclaration", "scope": 3050, "src": "1902:21:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3043, "name": "address", "nodeType": "ElementaryTypeName", "src": "1902:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3046, "indexed": true, "name": "spender", "nodeType": "VariableDeclaration", "scope": 3050, "src": "1933:23:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3045, "name": "address", "nodeType": "ElementaryTypeName", "src": "1933:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3048, "indexed": false, "name": "value", "nodeType": "VariableDeclaration", "scope": 3050, "src": "1966:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3047, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1966:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1892:93:3"}, "src": "1878:108:3"}, {"anonymous": false, "documentation": null, "id": 3056, "name": "Burn", "nodeType": "EventDefinition", "parameters": {"id": 3055, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3052, "indexed": true, "name": "from", "nodeType": "VariableDeclaration", "scope": 3056, "src": "2002:20:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3051, "name": "address", "nodeType": "ElementaryTypeName", "src": "2002:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3054, "indexed": false, "name": "amount", "nodeType": "VariableDeclaration", "scope": 3056, "src": "2024:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3053, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2024:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2001:38:3"}, "src": "1991:49:3"}, {"anonymous": false, "documentation": null, "id": 3062, "name": "Mint", "nodeType": "EventDefinition", "parameters": {"id": 3061, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3058, "indexed": true, "name": "to", "nodeType": "VariableDeclaration", "scope": 3062, "src": "2056:18:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3057, "name": "address", "nodeType": "ElementaryTypeName", "src": "2056:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3060, "indexed": false, "name": "amount", "nodeType": "VariableDeclaration", "scope": 3062, "src": "2076:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3059, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2076:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2055:36:3"}, "src": "2045:47:3"}, {"anonymous": false, "documentation": null, "id": 3064, "name": "MintFinished", "nodeType": "EventDefinition", "parameters": {"id": 3063, "nodeType": "ParameterList", "parameters": [], "src": "2115:2:3"}, "src": "2097:21:3"}, {"body": {"id": 3075, "nodeType": "Block", "src": "2401:40:3", "statements": [{"expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3071, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "2418:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3073, "indexExpression": {"argumentTypes": null, "id": 3072, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3066, "src": "2427:6:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2418:16:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 3070, "id": 3074, "nodeType": "Return", "src": "2411:23:3"}]}, "documentation": "@dev Gets the balance of the specified address.\n@param _owner The address to query the the balance of.\n@return An uint256 representing the amount owned by the passed address.", "id": 3076, "implemented": true, "kind": "function", "modifiers": [], "name": "balanceOf", "nodeType": "FunctionDefinition", "parameters": {"id": 3067, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3066, "name": "_owner", "nodeType": "VariableDeclaration", "scope": 3076, "src": "2355:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3065, "name": "address", "nodeType": "ElementaryTypeName", "src": "2355:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "2354:16:3"}, "returnParameters": {"id": 3070, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3069, "name": "", "nodeType": "VariableDeclaration", "scope": 3076, "src": "2392:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3068, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2392:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2391:9:3"}, "scope": 3338, "src": "2336:105:3", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 3136, "nodeType": "Block", "src": "2680:350:3", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 3090, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 3086, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3078, "src": "2698:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "hexValue": "30", "id": 3088, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2713:1:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 3087, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2705:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": "address"}, "id": 3089, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2705:10:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "src": "2698:17:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 3085, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "src": "2690:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 3091, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2690:26:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3092, "nodeType": "ExpressionStatement", "src": "2690:26:3"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3099, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 3094, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3080, "src": "2747:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3095, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "2757:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3098, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3096, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "2766:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3097, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2766:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2757:19:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2747:29:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "6d73672e73656e64657220646f65736e2774206861766520656e6f7567682062616c616e6365", "id": 3100, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2790:40:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_7f7b785a575bf86f94fdfe1af7672bedd6e81980b0474355876cde2604e523d0", "typeString": "literal_string \"msg.sender doesn't have enough balance\""}, "value": "msg.sender doesn't have enough balance"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_7f7b785a575bf86f94fdfe1af7672bedd6e81980b0474355876cde2604e523d0", "typeString": "literal_string \"msg.sender doesn't have enough balance\""}], "id": 3093, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "2726:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3101, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2726:114:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3102, "nodeType": "ExpressionStatement", "src": "2726:114:3"}, {"expression": {"argumentTypes": null, "id": 3114, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3103, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "2851:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3106, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3104, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "2860:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3105, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2860:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2851:19:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3112, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3080, "src": "2897:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3107, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "2873:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3110, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3108, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "2882:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3109, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2882:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2873:19:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 2978, "src": "2873:23:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3113, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2873:31:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2851:53:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3115, "nodeType": "ExpressionStatement", "src": "2851:53:3"}, {"expression": {"argumentTypes": null, "id": 3125, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3116, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "2914:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3118, "indexExpression": {"argumentTypes": null, "id": 3117, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3078, "src": "2923:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2914:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3123, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3080, "src": "2948:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3119, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "2930:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3121, "indexExpression": {"argumentTypes": null, "id": 3120, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3078, "src": "2939:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2930:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3122, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 3002, "src": "2930:17:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3124, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2930:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2914:41:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3126, "nodeType": "ExpressionStatement", "src": "2914:41:3"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3128, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "2979:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3129, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2979:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 3130, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3078, "src": "2990:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 3131, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3080, "src": "2995:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 3127, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3042, "src": "2970:8:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 3132, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2970:32:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3133, "nodeType": "EmitStatement", "src": "2965:37:3"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 3134, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3019:4:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 3084, "id": 3135, "nodeType": "Return", "src": "3012:11:3"}]}, "documentation": "@dev transfer token for a specified address\n@param _to The address to transfer to.\n@param _value The amount to be transferred.", "id": 3137, "implemented": true, "kind": "function", "modifiers": [], "name": "transfer", "nodeType": "FunctionDefinition", "parameters": {"id": 3081, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3078, "name": "_to", "nodeType": "VariableDeclaration", "scope": 3137, "src": "2629:11:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3077, "name": "address", "nodeType": "ElementaryTypeName", "src": "2629:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3080, "name": "_value", "nodeType": "VariableDeclaration", "scope": 3137, "src": "2642:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3079, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2642:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "2628:29:3"}, "returnParameters": {"id": 3084, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3083, "name": "", "nodeType": "VariableDeclaration", "scope": 3137, "src": "2674:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3082, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2674:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "2673:6:3"}, "scope": 3338, "src": "2611:419:3", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 3183, "nodeType": "Block", "src": "3386:212:3", "statements": [{"expression": {"argumentTypes": null, "id": 3155, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 3150, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3034, "src": "3396:12:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3153, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3141, "src": "3428:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 3151, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3034, "src": "3411:12:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3152, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 3002, "src": "3411:16:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3154, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3411:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3396:40:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3156, "nodeType": "ExpressionStatement", "src": "3396:40:3"}, {"expression": {"argumentTypes": null, "id": 3166, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3157, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "3446:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3159, "indexExpression": {"argumentTypes": null, "id": 3158, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3139, "src": "3455:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3446:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3164, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3141, "src": "3480:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3160, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "3462:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3162, "indexExpression": {"argumentTypes": null, "id": 3161, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3139, "src": "3471:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3462:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3163, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 3002, "src": "3462:17:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3165, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3462:26:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3446:42:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3167, "nodeType": "ExpressionStatement", "src": "3446:42:3"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3169, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3139, "src": "3508:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 3170, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3141, "src": "3513:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 3168, "name": "Mint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3062, "src": "3503:4:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 3171, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3503:18:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3172, "nodeType": "EmitStatement", "src": "3498:23:3"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "arguments": [{"argumentTypes": null, "hexValue": "30", "id": 3175, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3553:1:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 3174, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3545:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": "address"}, "id": 3176, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3545:10:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, {"argumentTypes": null, "id": 3177, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3139, "src": "3557:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 3178, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3141, "src": "3562:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 3173, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3042, "src": "3536:8:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 3179, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3536:34:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3180, "nodeType": "EmitStatement", "src": "3531:39:3"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 3181, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3587:4:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 3149, "id": 3182, "nodeType": "Return", "src": "3580:11:3"}]}, "documentation": "@dev Function to mint tokens\n@param _to The address that will receive the minted tokens.\n@param _amount The amount of tokens to mint.\n@return A boolean that indicates if the operation was successful.", "id": 3184, "implemented": true, "kind": "function", "modifiers": [{"arguments": null, "id": 3144, "modifierName": {"argumentTypes": null, "id": 3143, "name": "only<PERSON><PERSON>er", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3240, "src": "3353:9:3", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "3353:9:3"}, {"arguments": null, "id": 3146, "modifierName": {"argumentTypes": null, "id": 3145, "name": "canMint", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3249, "src": "3363:7:3", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "3363:7:3"}], "name": "mint", "nodeType": "FunctionDefinition", "parameters": {"id": 3142, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3139, "name": "_to", "nodeType": "VariableDeclaration", "scope": 3184, "src": "3303:11:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3138, "name": "address", "nodeType": "ElementaryTypeName", "src": "3303:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3141, "name": "_amount", "nodeType": "VariableDeclaration", "scope": 3184, "src": "3324:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3140, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3324:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "3293:52:3"}, "returnParameters": {"id": 3149, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3148, "name": "", "nodeType": "VariableDeclaration", "scope": 3184, "src": "3380:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3147, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3380:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "3379:6:3"}, "scope": 3338, "src": "3280:318:3", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 3220, "nodeType": "Block", "src": "3762:187:3", "statements": [{"expression": {"argumentTypes": null, "id": 3200, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 3195, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3034, "src": "3772:12:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3198, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3188, "src": "3804:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 3196, "name": "totalSupply_", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3034, "src": "3787:12:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3197, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 2978, "src": "3787:16:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3199, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3787:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3772:40:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3201, "nodeType": "ExpressionStatement", "src": "3772:40:3"}, {"expression": {"argumentTypes": null, "id": 3211, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3202, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "3822:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3204, "indexExpression": {"argumentTypes": null, "id": 3203, "name": "_tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3186, "src": "3831:11:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3822:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3209, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3188, "src": "3872:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3205, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "3846:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3207, "indexExpression": {"argumentTypes": null, "id": 3206, "name": "_tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3186, "src": "3855:11:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3846:21:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3208, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 2978, "src": "3846:25:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3210, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3846:34:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3822:58:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3212, "nodeType": "ExpressionStatement", "src": "3822:58:3"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3214, "name": "_tokenOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3186, "src": "3900:11:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 3215, "name": "_amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3188, "src": "3913:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 3213, "name": "Burn", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3056, "src": "3895:4:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,uint256)"}}, "id": 3216, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "3895:26:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3217, "nodeType": "EmitStatement", "src": "3890:31:3"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 3218, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3938:4:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 3194, "id": 3219, "nodeType": "Return", "src": "3931:11:3"}]}, "documentation": "@dev Function to burn tokens", "id": 3221, "implemented": true, "kind": "function", "modifiers": [{"arguments": null, "id": 3191, "modifierName": {"argumentTypes": null, "id": 3190, "name": "only<PERSON><PERSON>er", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3240, "src": "3737:9:3", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "3737:9:3"}], "name": "burn", "nodeType": "FunctionDefinition", "parameters": {"id": 3189, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3186, "name": "_tokenOwner", "nodeType": "VariableDeclaration", "scope": 3221, "src": "3679:19:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3185, "name": "address", "nodeType": "ElementaryTypeName", "src": "3679:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3188, "name": "_amount", "nodeType": "VariableDeclaration", "scope": 3221, "src": "3708:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3187, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3708:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "3669:60:3"}, "returnParameters": {"id": 3194, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3193, "name": "", "nodeType": "VariableDeclaration", "scope": 3221, "src": "3756:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3192, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3756:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "3755:6:3"}, "scope": 3338, "src": "3656:293:3", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 3228, "nodeType": "Block", "src": "4005:29:3", "statements": [{"expression": {"argumentTypes": null, "id": 3226, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3013, "src": "4022:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 3225, "id": 3227, "nodeType": "Return", "src": "4015:12:3"}]}, "documentation": null, "id": 3229, "implemented": true, "kind": "function", "modifiers": [], "name": "get<PERSON>wner", "nodeType": "FunctionDefinition", "parameters": {"id": 3222, "nodeType": "ParameterList", "parameters": [], "src": "3972:2:3"}, "returnParameters": {"id": 3225, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3224, "name": "", "nodeType": "VariableDeclaration", "scope": 3229, "src": "3996:7:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3223, "name": "address", "nodeType": "ElementaryTypeName", "src": "3996:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "3995:9:3"}, "scope": 3338, "src": "3955:79:3", "stateMutability": "view", "superFunction": null, "visibility": "public"}, {"body": {"id": 3239, "nodeType": "Block", "src": "4061:56:3", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 3235, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3232, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4946, "src": "4079:3:3", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 3233, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "4079:10:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"argumentTypes": null, "id": 3234, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3013, "src": "4093:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "4079:19:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 3231, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "src": "4071:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 3236, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4071:28:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3237, "nodeType": "ExpressionStatement", "src": "4071:28:3"}, {"id": 3238, "nodeType": "PlaceholderStatement", "src": "4109:1:3"}]}, "documentation": null, "id": 3240, "name": "only<PERSON><PERSON>er", "nodeType": "ModifierDefinition", "parameters": {"id": 3230, "nodeType": "ParameterList", "parameters": [], "src": "4058:2:3"}, "src": "4040:77:3", "visibility": "internal"}, {"body": {"id": 3248, "nodeType": "Block", "src": "4142:53:3", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3244, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4160:16:3", "subExpression": {"argumentTypes": null, "id": 3243, "name": "mintingFinished", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3009, "src": "4161:15:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 3242, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "src": "4152:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 3245, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4152:25:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3246, "nodeType": "ExpressionStatement", "src": "4152:25:3"}, {"id": 3247, "nodeType": "PlaceholderStatement", "src": "4187:1:3"}]}, "documentation": null, "id": 3249, "name": "canMint", "nodeType": "ModifierDefinition", "parameters": {"id": 3241, "nodeType": "ParameterList", "parameters": [], "src": "4139:2:3"}, "src": "4123:72:3", "visibility": "internal"}, {"body": {"id": 3336, "nodeType": "Block", "src": "4901:491:3", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 3265, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 3261, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3253, "src": "4919:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "hexValue": "30", "id": 3263, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4934:1:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 3262, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4926:7:3", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": "address"}, "id": 3264, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4926:10:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "src": "4919:17:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 3260, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "src": "4911:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$returns$__$", "typeString": "function (bool) pure"}}, "id": 3266, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4911:26:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3267, "nodeType": "ExpressionStatement", "src": "4911:26:3"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3273, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 3269, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3255, "src": "4955:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3270, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "4965:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3272, "indexExpression": {"argumentTypes": null, "id": 3271, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "4974:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4965:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4955:25:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "46726f6d20646f65736e2774206861766520656e6f7567682062616c616e6365", "id": 3274, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "4982:34:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_ce42a17daeaf718c0f1b34bbe14453c7b1813857f0ec2c1766b69fda815bc31d", "typeString": "literal_string \"From doesn't have enough balance\""}, "value": "From doesn't have enough balance"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ce42a17daeaf718c0f1b34bbe14453c7b1813857f0ec2c1766b69fda815bc31d", "typeString": "literal_string \"From doesn't have enough balance\""}], "id": 3268, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "4947:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3275, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "4947:70:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3276, "nodeType": "ExpressionStatement", "src": "4947:70:3"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 3285, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 3278, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3255, "src": "5048:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3279, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3019, "src": "5058:7:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 3281, "indexExpression": {"argumentTypes": null, "id": 3280, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "5066:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5058:14:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3284, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3282, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "5073:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3283, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "5073:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5058:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5048:35:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "4e6f7420616c6c6f77656420746f207370656e642074686973206d756368", "id": 3286, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5097:32:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_f83afab5a2fdc018f2f2409afdd945b8b74c8752fac2171cdb87f94c6eb2b8be", "typeString": "literal_string \"Not allowed to spend this much\""}, "value": "Not allowed to spend this much"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_f83afab5a2fdc018f2f2409afdd945b8b74c8752fac2171cdb87f94c6eb2b8be", "typeString": "literal_string \"Not allowed to spend this much\""}], "id": 3277, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "src": "5027:7:3", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 3287, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5027:112:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3288, "nodeType": "ExpressionStatement", "src": "5027:112:3"}, {"expression": {"argumentTypes": null, "id": 3298, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3289, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "5150:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3291, "indexExpression": {"argumentTypes": null, "id": 3290, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "5159:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5150:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3296, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3255, "src": "5188:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3292, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "5168:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3294, "indexExpression": {"argumentTypes": null, "id": 3293, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "5177:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5168:15:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3295, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 2978, "src": "5168:19:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3297, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5168:27:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5150:45:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3299, "nodeType": "ExpressionStatement", "src": "5150:45:3"}, {"expression": {"argumentTypes": null, "id": 3309, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3300, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "5205:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3302, "indexExpression": {"argumentTypes": null, "id": 3301, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3253, "src": "5214:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5205:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3307, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3255, "src": "5239:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3303, "name": "balances", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3023, "src": "5221:8:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3305, "indexExpression": {"argumentTypes": null, "id": 3304, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3253, "src": "5230:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5221:13:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3306, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "add", "nodeType": "MemberAccess", "referencedDeclaration": 3002, "src": "5221:17:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3308, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5221:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5205:41:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3310, "nodeType": "ExpressionStatement", "src": "5205:41:3"}, {"expression": {"argumentTypes": null, "id": 3326, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3311, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3019, "src": "5256:7:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 3315, "indexExpression": {"argumentTypes": null, "id": 3312, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "5264:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5256:14:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3316, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3313, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "5271:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3314, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "5271:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5256:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3324, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3255, "src": "5314:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 3317, "name": "allowed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3019, "src": "5284:7:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_mapping$_t_address_$_t_uint256_$_$", "typeString": "mapping(address => mapping(address => uint256))"}}, "id": 3319, "indexExpression": {"argumentTypes": null, "id": 3318, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "5292:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5284:14:3", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_uint256_$", "typeString": "mapping(address => uint256)"}}, "id": 3322, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 3320, "name": "tx", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 4958, "src": "5299:2:3", "typeDescriptions": {"typeIdentifier": "t_magic_transaction", "typeString": "tx"}}, "id": 3321, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "origin", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "5299:9:3", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "5284:25:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3323, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sub", "nodeType": "MemberAccess", "referencedDeclaration": 2978, "src": "5284:29:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_pure$_t_uint256_$_t_uint256_$returns$_t_uint256_$bound_to$_t_uint256_$", "typeString": "function (uint256,uint256) pure returns (uint256)"}}, "id": 3325, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5284:37:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5256:65:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 3327, "nodeType": "ExpressionStatement", "src": "5256:65:3"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 3329, "name": "_from", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3251, "src": "5345:5:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 3330, "name": "_to", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3253, "src": "5352:3:3", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"argumentTypes": null, "id": 3331, "name": "_value", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3255, "src": "5357:6:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 3328, "name": "Transfer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 3042, "src": "5336:8:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$_t_uint256_$returns$__$", "typeString": "function (address,address,uint256)"}}, "id": 3332, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "5336:28:3", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 3333, "nodeType": "EmitStatement", "src": "5331:33:3"}, {"expression": {"argumentTypes": null, "hexValue": "74727565", "id": 3334, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5381:4:3", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "functionReturnParameters": 3259, "id": 3335, "nodeType": "Return", "src": "5374:11:3"}]}, "documentation": "@dev Transfer tokens from one address to another\n@param _from address The address which you want to send tokens from\n@param _to address The address which you want to transfer to\n@param _value uint256 the amount of tokens to be transferred", "id": 3337, "implemented": true, "kind": "function", "modifiers": [], "name": "transferFrom", "nodeType": "FunctionDefinition", "parameters": {"id": 3256, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3251, "name": "_from", "nodeType": "VariableDeclaration", "scope": 3337, "src": "4814:13:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3250, "name": "address", "nodeType": "ElementaryTypeName", "src": "4814:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3253, "name": "_to", "nodeType": "VariableDeclaration", "scope": 3337, "src": "4837:11:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 3252, "name": "address", "nodeType": "ElementaryTypeName", "src": "4837:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 3255, "name": "_value", "nodeType": "VariableDeclaration", "scope": 3337, "src": "4858:14:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3254, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4858:7:3", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "4804:74:3"}, "returnParameters": {"id": 3259, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 3258, "name": "", "nodeType": "VariableDeclaration", "scope": 3337, "src": "4895:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 3257, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4895:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "value": null, "visibility": "internal"}], "src": "4894:6:3"}, "scope": 3338, "src": "4783:609:3", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}], "scope": 3339, "src": "1368:5575:3"}], "src": "0:6944:3"}, "legacyAST": {"attributes": {"absolutePath": "project:/contracts/ERC20.sol", "exportedSymbols": {"ERC20": [3338], "SafeMath": [3003]}}, "children": [{"attributes": {"literals": ["solidity", "^", "0.5", ".0"]}, "id": 2911, "name": "PragmaDirective", "src": "0:23:3"}, {"attributes": {"baseContracts": [null], "contractDependencies": [null], "contractKind": "library", "documentation": "@title SafeMath\n@dev Math operations with safety checks that throw on error", "fullyImplemented": true, "linearizedBaseContracts": [3003], "name": "SafeMath", "scope": 3339}, "children": [{"attributes": {"documentation": "@dev Multiplies two numbers, throws on overflow.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "mul", "scope": 3003, "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, "children": [{"children": [{"attributes": {"constant": false, "name": "a", "scope": 2944, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2912, "name": "ElementaryTypeName", "src": "349:7:3"}], "id": 2913, "name": "VariableDeclaration", "src": "349:9:3"}, {"attributes": {"constant": false, "name": "b", "scope": 2944, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2914, "name": "ElementaryTypeName", "src": "360:7:3"}], "id": 2915, "name": "VariableDeclaration", "src": "360:9:3"}], "id": 2916, "name": "ParameterList", "src": "348:22:3"}, {"children": [{"attributes": {"constant": false, "name": "c", "scope": 2944, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2917, "name": "ElementaryTypeName", "src": "394:7:3"}], "id": 2918, "name": "VariableDeclaration", "src": "394:9:3"}], "id": 2919, "name": "ParameterList", "src": "393:11:3"}, {"children": [{"attributes": {"falseBody": null}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "==", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2913, "type": "uint256", "value": "a"}, "id": 2920, "name": "Identifier", "src": "419:1:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 2921, "name": "Literal", "src": "424:1:3"}], "id": 2922, "name": "BinaryOperation", "src": "419:6:3"}, {"children": [{"attributes": {"functionReturnParameters": 2919}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 2923, "name": "Literal", "src": "448:1:3"}], "id": 2924, "name": "Return", "src": "441:8:3"}], "id": 2925, "name": "Block", "src": "427:33:3"}], "id": 2926, "name": "IfStatement", "src": "415:45:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2918, "type": "uint256", "value": "c"}, "id": 2927, "name": "Identifier", "src": "469:1:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "*", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2913, "type": "uint256", "value": "a"}, "id": 2928, "name": "Identifier", "src": "473:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2915, "type": "uint256", "value": "b"}, "id": 2929, "name": "Identifier", "src": "477:1:3"}], "id": 2930, "name": "BinaryOperation", "src": "473:5:3"}], "id": 2931, "name": "Assignment", "src": "469:9:3"}], "id": 2932, "name": "ExpressionStatement", "src": "469:9:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [null], "referencedDeclaration": 4935, "type": "function (bool) pure", "value": "assert"}, "id": 2933, "name": "Identifier", "src": "488:6:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "==", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "/", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2918, "type": "uint256", "value": "c"}, "id": 2934, "name": "Identifier", "src": "495:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2913, "type": "uint256", "value": "a"}, "id": 2935, "name": "Identifier", "src": "499:1:3"}], "id": 2936, "name": "BinaryOperation", "src": "495:5:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2915, "type": "uint256", "value": "b"}, "id": 2937, "name": "Identifier", "src": "504:1:3"}], "id": 2938, "name": "BinaryOperation", "src": "495:10:3"}], "id": 2939, "name": "FunctionCall", "src": "488:18:3"}], "id": 2940, "name": "ExpressionStatement", "src": "488:18:3"}, {"attributes": {"functionReturnParameters": 2919}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2918, "type": "uint256", "value": "c"}, "id": 2941, "name": "Identifier", "src": "523:1:3"}], "id": 2942, "name": "Return", "src": "516:8:3"}], "id": 2943, "name": "Block", "src": "405:126:3"}], "id": 2944, "name": "FunctionDefinition", "src": "336:195:3"}, {"attributes": {"documentation": "@dev Integer division of two numbers, truncating the quotient.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "div", "scope": 3003, "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, "children": [{"children": [{"attributes": {"constant": false, "name": "a", "scope": 2958, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2945, "name": "ElementaryTypeName", "src": "636:7:3"}], "id": 2946, "name": "VariableDeclaration", "src": "636:9:3"}, {"attributes": {"constant": false, "name": "b", "scope": 2958, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2947, "name": "ElementaryTypeName", "src": "647:7:3"}], "id": 2948, "name": "VariableDeclaration", "src": "647:9:3"}], "id": 2949, "name": "ParameterList", "src": "635:22:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 2958, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2950, "name": "ElementaryTypeName", "src": "681:7:3"}], "id": 2951, "name": "VariableDeclaration", "src": "681:7:3"}], "id": 2952, "name": "ParameterList", "src": "680:9:3"}, {"children": [{"attributes": {"functionReturnParameters": 2952}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "/", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2946, "type": "uint256", "value": "a"}, "id": 2953, "name": "Identifier", "src": "901:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2948, "type": "uint256", "value": "b"}, "id": 2954, "name": "Identifier", "src": "905:1:3"}], "id": 2955, "name": "BinaryOperation", "src": "901:5:3"}], "id": 2956, "name": "Return", "src": "894:12:3"}], "id": 2957, "name": "Block", "src": "690:223:3"}], "id": 2958, "name": "FunctionDefinition", "src": "623:290:3"}, {"attributes": {"documentation": "@dev Subtracts two numbers, throws on overflow (i.e. if subtrahend is greater than minuend).", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "sub", "scope": 3003, "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, "children": [{"children": [{"attributes": {"constant": false, "name": "a", "scope": 2978, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2959, "name": "ElementaryTypeName", "src": "1048:7:3"}], "id": 2960, "name": "VariableDeclaration", "src": "1048:9:3"}, {"attributes": {"constant": false, "name": "b", "scope": 2978, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2961, "name": "ElementaryTypeName", "src": "1059:7:3"}], "id": 2962, "name": "VariableDeclaration", "src": "1059:9:3"}], "id": 2963, "name": "ParameterList", "src": "1047:22:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 2978, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2964, "name": "ElementaryTypeName", "src": "1093:7:3"}], "id": 2965, "name": "VariableDeclaration", "src": "1093:7:3"}], "id": 2966, "name": "ParameterList", "src": "1092:9:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [null], "referencedDeclaration": 4935, "type": "function (bool) pure", "value": "assert"}, "id": 2967, "name": "Identifier", "src": "1112:6:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2962, "type": "uint256", "value": "b"}, "id": 2968, "name": "Identifier", "src": "1119:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2960, "type": "uint256", "value": "a"}, "id": 2969, "name": "Identifier", "src": "1124:1:3"}], "id": 2970, "name": "BinaryOperation", "src": "1119:6:3"}], "id": 2971, "name": "FunctionCall", "src": "1112:14:3"}], "id": 2972, "name": "ExpressionStatement", "src": "1112:14:3"}, {"attributes": {"functionReturnParameters": 2966}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "-", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2960, "type": "uint256", "value": "a"}, "id": 2973, "name": "Identifier", "src": "1143:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2962, "type": "uint256", "value": "b"}, "id": 2974, "name": "Identifier", "src": "1147:1:3"}], "id": 2975, "name": "BinaryOperation", "src": "1143:5:3"}], "id": 2976, "name": "Return", "src": "1136:12:3"}], "id": 2977, "name": "Block", "src": "1102:53:3"}], "id": 2978, "name": "FunctionDefinition", "src": "1035:120:3"}, {"attributes": {"documentation": "@dev Adds two numbers, throws on overflow.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "add", "scope": 3003, "stateMutability": "pure", "superFunction": null, "visibility": "internal"}, "children": [{"children": [{"attributes": {"constant": false, "name": "a", "scope": 3002, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2979, "name": "ElementaryTypeName", "src": "1240:7:3"}], "id": 2980, "name": "VariableDeclaration", "src": "1240:9:3"}, {"attributes": {"constant": false, "name": "b", "scope": 3002, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2981, "name": "ElementaryTypeName", "src": "1251:7:3"}], "id": 2982, "name": "VariableDeclaration", "src": "1251:9:3"}], "id": 2983, "name": "ParameterList", "src": "1239:22:3"}, {"children": [{"attributes": {"constant": false, "name": "c", "scope": 3002, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 2984, "name": "ElementaryTypeName", "src": "1285:7:3"}], "id": 2985, "name": "VariableDeclaration", "src": "1285:9:3"}], "id": 2986, "name": "ParameterList", "src": "1284:11:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2985, "type": "uint256", "value": "c"}, "id": 2987, "name": "Identifier", "src": "1306:1:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "+", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2980, "type": "uint256", "value": "a"}, "id": 2988, "name": "Identifier", "src": "1310:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2982, "type": "uint256", "value": "b"}, "id": 2989, "name": "Identifier", "src": "1314:1:3"}], "id": 2990, "name": "BinaryOperation", "src": "1310:5:3"}], "id": 2991, "name": "Assignment", "src": "1306:9:3"}], "id": 2992, "name": "ExpressionStatement", "src": "1306:9:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [null], "referencedDeclaration": 4935, "type": "function (bool) pure", "value": "assert"}, "id": 2993, "name": "Identifier", "src": "1325:6:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": ">=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2985, "type": "uint256", "value": "c"}, "id": 2994, "name": "Identifier", "src": "1332:1:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2980, "type": "uint256", "value": "a"}, "id": 2995, "name": "Identifier", "src": "1337:1:3"}], "id": 2996, "name": "BinaryOperation", "src": "1332:6:3"}], "id": 2997, "name": "FunctionCall", "src": "1325:14:3"}], "id": 2998, "name": "ExpressionStatement", "src": "1325:14:3"}, {"attributes": {"functionReturnParameters": 2986}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 2985, "type": "uint256", "value": "c"}, "id": 2999, "name": "Identifier", "src": "1356:1:3"}], "id": 3000, "name": "Return", "src": "1349:8:3"}], "id": 3001, "name": "Block", "src": "1296:68:3"}], "id": 3002, "name": "FunctionDefinition", "src": "1227:137:3"}], "id": 3003, "name": "ContractDefinition", "src": "241:1125:3"}, {"attributes": {"baseContracts": [null], "contractDependencies": [null], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "linearizedBaseContracts": [3338], "name": "ERC20", "scope": 3339}, "children": [{"children": [{"attributes": {"contractScope": null, "name": "SafeMath", "referencedDeclaration": 3003, "type": "library SafeMath"}, "id": 3004, "name": "UserDefinedTypeName", "src": "1395:8:3"}, {"attributes": {"name": "uint256", "type": "uint256"}, "id": 3005, "name": "ElementaryTypeName", "src": "1408:7:3"}], "id": 3006, "name": "UsingForDirective", "src": "1389:27:3"}, {"attributes": {"constant": false, "name": "mintingFinished", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "bool", "visibility": "public"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 3007, "name": "ElementaryTypeName", "src": "1422:4:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "66616c7365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "false"}, "id": 3008, "name": "Literal", "src": "1452:5:3"}], "id": 3009, "name": "VariableDeclaration", "src": "1422:35:3"}, {"attributes": {"constant": false, "name": "owner", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "address", "visibility": "public"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3010, "name": "ElementaryTypeName", "src": "1464:7:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4946, "type": "msg", "value": "msg"}, "id": 3011, "name": "Identifier", "src": "1487:3:3"}], "id": 3012, "name": "MemberAccess", "src": "1487:10:3"}], "id": 3013, "name": "VariableDeclaration", "src": "1464:33:3"}, {"attributes": {"constant": false, "name": "allowed", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => mapping(address => uint256))", "value": null, "visibility": "internal"}, "children": [{"attributes": {"type": "mapping(address => mapping(address => uint256))"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 3014, "name": "ElementaryTypeName", "src": "1512:7:3"}, {"attributes": {"type": "mapping(address => uint256)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 3015, "name": "ElementaryTypeName", "src": "1531:7:3"}, {"attributes": {"name": "uint256", "type": "uint256"}, "id": 3016, "name": "ElementaryTypeName", "src": "1542:7:3"}], "id": 3017, "name": "Mapping", "src": "1523:27:3"}], "id": 3018, "name": "Mapping", "src": "1504:47:3"}], "id": 3019, "name": "VariableDeclaration", "src": "1504:64:3"}, {"attributes": {"constant": false, "name": "balances", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => uint256)", "value": null, "visibility": "internal"}, "children": [{"attributes": {"type": "mapping(address => uint256)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 3020, "name": "ElementaryTypeName", "src": "1582:7:3"}, {"attributes": {"name": "uint256", "type": "uint256"}, "id": 3021, "name": "ElementaryTypeName", "src": "1593:7:3"}], "id": 3022, "name": "Mapping", "src": "1574:27:3"}], "id": 3023, "name": "VariableDeclaration", "src": "1574:36:3"}, {"attributes": {"constant": true, "name": "name", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "string", "visibility": "public"}, "children": [{"attributes": {"name": "string", "type": "string"}, "id": 3024, "name": "ElementaryTypeName", "src": "1617:6:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "45636f584368616e676520546f6b656e", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"EcoXChange Token\"", "value": "EcoXChange Token"}, "id": 3025, "name": "Literal", "src": "1647:18:3"}], "id": 3026, "name": "VariableDeclaration", "src": "1617:48:3"}, {"attributes": {"constant": true, "name": "symbol", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "string", "visibility": "public"}, "children": [{"attributes": {"name": "string", "type": "string"}, "id": 3027, "name": "ElementaryTypeName", "src": "1671:6:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "455843", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"EXC\"", "value": "EXC"}, "id": 3028, "name": "Literal", "src": "1703:5:3"}], "id": 3029, "name": "VariableDeclaration", "src": "1671:37:3"}, {"attributes": {"constant": true, "name": "decimals", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "uint8", "visibility": "public"}, "children": [{"attributes": {"name": "uint8", "type": "uint8"}, "id": 3030, "name": "ElementaryTypeName", "src": "1714:5:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "3138", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 18", "value": "18"}, "id": 3031, "name": "Literal", "src": "1747:2:3"}], "id": 3032, "name": "VariableDeclaration", "src": "1714:35:3"}, {"attributes": {"constant": false, "name": "totalSupply_", "scope": 3338, "stateVariable": true, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3033, "name": "ElementaryTypeName", "src": "1774:7:3"}], "id": 3034, "name": "VariableDeclaration", "src": "1774:20:3"}, {"attributes": {"anonymous": false, "documentation": null, "name": "Transfer"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "from", "scope": 3042, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3035, "name": "ElementaryTypeName", "src": "1816:7:3"}], "id": 3036, "name": "VariableDeclaration", "src": "1816:20:3"}, {"attributes": {"constant": false, "indexed": true, "name": "to", "scope": 3042, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3037, "name": "ElementaryTypeName", "src": "1838:7:3"}], "id": 3038, "name": "VariableDeclaration", "src": "1838:18:3"}, {"attributes": {"constant": false, "indexed": false, "name": "value", "scope": 3042, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3039, "name": "ElementaryTypeName", "src": "1858:7:3"}], "id": 3040, "name": "VariableDeclaration", "src": "1858:13:3"}], "id": 3041, "name": "ParameterList", "src": "1815:57:3"}], "id": 3042, "name": "EventDefinition", "src": "1801:72:3"}, {"attributes": {"anonymous": false, "documentation": null, "name": "Approval"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "owner", "scope": 3050, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3043, "name": "ElementaryTypeName", "src": "1902:7:3"}], "id": 3044, "name": "VariableDeclaration", "src": "1902:21:3"}, {"attributes": {"constant": false, "indexed": true, "name": "spender", "scope": 3050, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3045, "name": "ElementaryTypeName", "src": "1933:7:3"}], "id": 3046, "name": "VariableDeclaration", "src": "1933:23:3"}, {"attributes": {"constant": false, "indexed": false, "name": "value", "scope": 3050, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3047, "name": "ElementaryTypeName", "src": "1966:7:3"}], "id": 3048, "name": "VariableDeclaration", "src": "1966:13:3"}], "id": 3049, "name": "ParameterList", "src": "1892:93:3"}], "id": 3050, "name": "EventDefinition", "src": "1878:108:3"}, {"attributes": {"anonymous": false, "documentation": null, "name": "Burn"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "from", "scope": 3056, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3051, "name": "ElementaryTypeName", "src": "2002:7:3"}], "id": 3052, "name": "VariableDeclaration", "src": "2002:20:3"}, {"attributes": {"constant": false, "indexed": false, "name": "amount", "scope": 3056, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3053, "name": "ElementaryTypeName", "src": "2024:7:3"}], "id": 3054, "name": "VariableDeclaration", "src": "2024:14:3"}], "id": 3055, "name": "ParameterList", "src": "2001:38:3"}], "id": 3056, "name": "EventDefinition", "src": "1991:49:3"}, {"attributes": {"anonymous": false, "documentation": null, "name": "Mint"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "to", "scope": 3062, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3057, "name": "ElementaryTypeName", "src": "2056:7:3"}], "id": 3058, "name": "VariableDeclaration", "src": "2056:18:3"}, {"attributes": {"constant": false, "indexed": false, "name": "amount", "scope": 3062, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3059, "name": "ElementaryTypeName", "src": "2076:7:3"}], "id": 3060, "name": "VariableDeclaration", "src": "2076:14:3"}], "id": 3061, "name": "ParameterList", "src": "2055:36:3"}], "id": 3062, "name": "EventDefinition", "src": "2045:47:3"}, {"attributes": {"anonymous": false, "documentation": null, "name": "MintFinished"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 3063, "name": "ParameterList", "src": "2115:2:3"}], "id": 3064, "name": "EventDefinition", "src": "2097:21:3"}, {"attributes": {"documentation": "@dev Gets the balance of the specified address.\n@param _owner The address to query the the balance of.\n@return An uint256 representing the amount owned by the passed address.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "balanceOf", "scope": 3338, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_owner", "scope": 3076, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3065, "name": "ElementaryTypeName", "src": "2355:7:3"}], "id": 3066, "name": "VariableDeclaration", "src": "2355:14:3"}], "id": 3067, "name": "ParameterList", "src": "2354:16:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 3076, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3068, "name": "ElementaryTypeName", "src": "2392:7:3"}], "id": 3069, "name": "VariableDeclaration", "src": "2392:7:3"}], "id": 3070, "name": "ParameterList", "src": "2391:9:3"}, {"children": [{"attributes": {"functionReturnParameters": 3070}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3071, "name": "Identifier", "src": "2418:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3066, "type": "address", "value": "_owner"}, "id": 3072, "name": "Identifier", "src": "2427:6:3"}], "id": 3073, "name": "IndexAccess", "src": "2418:16:3"}], "id": 3074, "name": "Return", "src": "2411:23:3"}], "id": 3075, "name": "Block", "src": "2401:40:3"}], "id": 3076, "name": "FunctionDefinition", "src": "2336:105:3"}, {"attributes": {"documentation": "@dev transfer token for a specified address\n@param _to The address to transfer to.\n@param _value The amount to be transferred.", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "transfer", "scope": 3338, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_to", "scope": 3137, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3077, "name": "ElementaryTypeName", "src": "2629:7:3"}], "id": 3078, "name": "VariableDeclaration", "src": "2629:11:3"}, {"attributes": {"constant": false, "name": "_value", "scope": 3137, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3079, "name": "ElementaryTypeName", "src": "2642:7:3"}], "id": 3080, "name": "VariableDeclaration", "src": "2642:14:3"}], "id": 3081, "name": "ParameterList", "src": "2628:29:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 3137, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 3082, "name": "ElementaryTypeName", "src": "2674:4:3"}], "id": 3083, "name": "VariableDeclaration", "src": "2674:4:3"}], "id": 3084, "name": "ParameterList", "src": "2673:6:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "type": "function (bool) pure", "value": "require"}, "id": 3085, "name": "Identifier", "src": "2690:7:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3078, "type": "address", "value": "_to"}, "id": 3086, "name": "Identifier", "src": "2698:3:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": true, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "address payable", "type_conversion": true}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "type": "type(address)", "value": "address"}, "id": 3087, "name": "ElementaryTypeNameExpression", "src": "2705:7:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 3088, "name": "Literal", "src": "2713:1:3"}], "id": 3089, "name": "FunctionCall", "src": "2705:10:3"}], "id": 3090, "name": "BinaryOperation", "src": "2698:17:3"}], "id": 3091, "name": "FunctionCall", "src": "2690:26:3"}], "id": 3092, "name": "ExpressionStatement", "src": "2690:26:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_7f7b785a575bf86f94fdfe1af7672bedd6e81980b0474355876cde2604e523d0", "typeString": "literal_string \"msg.sender doesn't have enough balance\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 3093, "name": "Identifier", "src": "2726:7:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3080, "type": "uint256", "value": "_value"}, "id": 3094, "name": "Identifier", "src": "2747:6:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3095, "name": "Identifier", "src": "2757:8:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3096, "name": "Identifier", "src": "2766:2:3"}], "id": 3097, "name": "MemberAccess", "src": "2766:9:3"}], "id": 3098, "name": "IndexAccess", "src": "2757:19:3"}], "id": 3099, "name": "BinaryOperation", "src": "2747:29:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "6d73672e73656e64657220646f65736e2774206861766520656e6f7567682062616c616e6365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"msg.sender doesn't have enough balance\"", "value": "msg.sender doesn't have enough balance"}, "id": 3100, "name": "Literal", "src": "2790:40:3"}], "id": 3101, "name": "FunctionCall", "src": "2726:114:3"}], "id": 3102, "name": "ExpressionStatement", "src": "2726:114:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3103, "name": "Identifier", "src": "2851:8:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3104, "name": "Identifier", "src": "2860:2:3"}], "id": 3105, "name": "MemberAccess", "src": "2860:9:3"}], "id": 3106, "name": "IndexAccess", "src": "2851:19:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sub", "referencedDeclaration": 2978, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3107, "name": "Identifier", "src": "2873:8:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3108, "name": "Identifier", "src": "2882:2:3"}], "id": 3109, "name": "MemberAccess", "src": "2882:9:3"}], "id": 3110, "name": "IndexAccess", "src": "2873:19:3"}], "id": 3111, "name": "MemberAccess", "src": "2873:23:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3080, "type": "uint256", "value": "_value"}, "id": 3112, "name": "Identifier", "src": "2897:6:3"}], "id": 3113, "name": "FunctionCall", "src": "2873:31:3"}], "id": 3114, "name": "Assignment", "src": "2851:53:3"}], "id": 3115, "name": "ExpressionStatement", "src": "2851:53:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3116, "name": "Identifier", "src": "2914:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3078, "type": "address", "value": "_to"}, "id": 3117, "name": "Identifier", "src": "2923:3:3"}], "id": 3118, "name": "IndexAccess", "src": "2914:13:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "add", "referencedDeclaration": 3002, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3119, "name": "Identifier", "src": "2930:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3078, "type": "address", "value": "_to"}, "id": 3120, "name": "Identifier", "src": "2939:3:3"}], "id": 3121, "name": "IndexAccess", "src": "2930:13:3"}], "id": 3122, "name": "MemberAccess", "src": "2930:17:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3080, "type": "uint256", "value": "_value"}, "id": 3123, "name": "Identifier", "src": "2948:6:3"}], "id": 3124, "name": "FunctionCall", "src": "2930:25:3"}], "id": 3125, "name": "Assignment", "src": "2914:41:3"}], "id": 3126, "name": "ExpressionStatement", "src": "2914:41:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 3042, "type": "function (address,address,uint256)", "value": "Transfer"}, "id": 3127, "name": "Identifier", "src": "2970:8:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3128, "name": "Identifier", "src": "2979:2:3"}], "id": 3129, "name": "MemberAccess", "src": "2979:9:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3078, "type": "address", "value": "_to"}, "id": 3130, "name": "Identifier", "src": "2990:3:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3080, "type": "uint256", "value": "_value"}, "id": 3131, "name": "Identifier", "src": "2995:6:3"}], "id": 3132, "name": "FunctionCall", "src": "2970:32:3"}], "id": 3133, "name": "EmitStatement", "src": "2965:37:3"}, {"attributes": {"functionReturnParameters": 3084}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 3134, "name": "Literal", "src": "3019:4:3"}], "id": 3135, "name": "Return", "src": "3012:11:3"}], "id": 3136, "name": "Block", "src": "2680:350:3"}], "id": 3137, "name": "FunctionDefinition", "src": "2611:419:3"}, {"attributes": {"documentation": "@dev Function to mint tokens\n@param _to The address that will receive the minted tokens.\n@param _amount The amount of tokens to mint.\n@return A boolean that indicates if the operation was successful.", "implemented": true, "isConstructor": false, "kind": "function", "name": "mint", "scope": 3338, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_to", "scope": 3184, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3138, "name": "ElementaryTypeName", "src": "3303:7:3"}], "id": 3139, "name": "VariableDeclaration", "src": "3303:11:3"}, {"attributes": {"constant": false, "name": "_amount", "scope": 3184, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3140, "name": "ElementaryTypeName", "src": "3324:7:3"}], "id": 3141, "name": "VariableDeclaration", "src": "3324:15:3"}], "id": 3142, "name": "ParameterList", "src": "3293:52:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 3184, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 3147, "name": "ElementaryTypeName", "src": "3380:4:3"}], "id": 3148, "name": "VariableDeclaration", "src": "3380:4:3"}], "id": 3149, "name": "ParameterList", "src": "3379:6:3"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3240, "type": "modifier ()", "value": "only<PERSON><PERSON>er"}, "id": 3143, "name": "Identifier", "src": "3353:9:3"}], "id": 3144, "name": "ModifierInvocation", "src": "3353:9:3"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3249, "type": "modifier ()", "value": "canMint"}, "id": 3145, "name": "Identifier", "src": "3363:7:3"}], "id": 3146, "name": "ModifierInvocation", "src": "3363:7:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3034, "type": "uint256", "value": "totalSupply_"}, "id": 3150, "name": "Identifier", "src": "3396:12:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "add", "referencedDeclaration": 3002, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3034, "type": "uint256", "value": "totalSupply_"}, "id": 3151, "name": "Identifier", "src": "3411:12:3"}], "id": 3152, "name": "MemberAccess", "src": "3411:16:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3141, "type": "uint256", "value": "_amount"}, "id": 3153, "name": "Identifier", "src": "3428:7:3"}], "id": 3154, "name": "FunctionCall", "src": "3411:25:3"}], "id": 3155, "name": "Assignment", "src": "3396:40:3"}], "id": 3156, "name": "ExpressionStatement", "src": "3396:40:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3157, "name": "Identifier", "src": "3446:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3139, "type": "address", "value": "_to"}, "id": 3158, "name": "Identifier", "src": "3455:3:3"}], "id": 3159, "name": "IndexAccess", "src": "3446:13:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "add", "referencedDeclaration": 3002, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3160, "name": "Identifier", "src": "3462:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3139, "type": "address", "value": "_to"}, "id": 3161, "name": "Identifier", "src": "3471:3:3"}], "id": 3162, "name": "IndexAccess", "src": "3462:13:3"}], "id": 3163, "name": "MemberAccess", "src": "3462:17:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3141, "type": "uint256", "value": "_amount"}, "id": 3164, "name": "Identifier", "src": "3480:7:3"}], "id": 3165, "name": "FunctionCall", "src": "3462:26:3"}], "id": 3166, "name": "Assignment", "src": "3446:42:3"}], "id": 3167, "name": "ExpressionStatement", "src": "3446:42:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 3062, "type": "function (address,uint256)", "value": "Mint"}, "id": 3168, "name": "Identifier", "src": "3503:4:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3139, "type": "address", "value": "_to"}, "id": 3169, "name": "Identifier", "src": "3508:3:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3141, "type": "uint256", "value": "_amount"}, "id": 3170, "name": "Identifier", "src": "3513:7:3"}], "id": 3171, "name": "FunctionCall", "src": "3503:18:3"}], "id": 3172, "name": "EmitStatement", "src": "3498:23:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address_payable", "typeString": "address payable"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 3042, "type": "function (address,address,uint256)", "value": "Transfer"}, "id": 3173, "name": "Identifier", "src": "3536:8:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": true, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "address payable", "type_conversion": true}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "type": "type(address)", "value": "address"}, "id": 3174, "name": "ElementaryTypeNameExpression", "src": "3545:7:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 3175, "name": "Literal", "src": "3553:1:3"}], "id": 3176, "name": "FunctionCall", "src": "3545:10:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3139, "type": "address", "value": "_to"}, "id": 3177, "name": "Identifier", "src": "3557:3:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3141, "type": "uint256", "value": "_amount"}, "id": 3178, "name": "Identifier", "src": "3562:7:3"}], "id": 3179, "name": "FunctionCall", "src": "3536:34:3"}], "id": 3180, "name": "EmitStatement", "src": "3531:39:3"}, {"attributes": {"functionReturnParameters": 3149}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 3181, "name": "Literal", "src": "3587:4:3"}], "id": 3182, "name": "Return", "src": "3580:11:3"}], "id": 3183, "name": "Block", "src": "3386:212:3"}], "id": 3184, "name": "FunctionDefinition", "src": "3280:318:3"}, {"attributes": {"documentation": "@dev Function to burn tokens", "implemented": true, "isConstructor": false, "kind": "function", "name": "burn", "scope": 3338, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_tokenOwner", "scope": 3221, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3185, "name": "ElementaryTypeName", "src": "3679:7:3"}], "id": 3186, "name": "VariableDeclaration", "src": "3679:19:3"}, {"attributes": {"constant": false, "name": "_amount", "scope": 3221, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3187, "name": "ElementaryTypeName", "src": "3708:7:3"}], "id": 3188, "name": "VariableDeclaration", "src": "3708:15:3"}], "id": 3189, "name": "ParameterList", "src": "3669:60:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 3221, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 3192, "name": "ElementaryTypeName", "src": "3756:4:3"}], "id": 3193, "name": "VariableDeclaration", "src": "3756:4:3"}], "id": 3194, "name": "ParameterList", "src": "3755:6:3"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3240, "type": "modifier ()", "value": "only<PERSON><PERSON>er"}, "id": 3190, "name": "Identifier", "src": "3737:9:3"}], "id": 3191, "name": "ModifierInvocation", "src": "3737:9:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3034, "type": "uint256", "value": "totalSupply_"}, "id": 3195, "name": "Identifier", "src": "3772:12:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sub", "referencedDeclaration": 2978, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3034, "type": "uint256", "value": "totalSupply_"}, "id": 3196, "name": "Identifier", "src": "3787:12:3"}], "id": 3197, "name": "MemberAccess", "src": "3787:16:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3188, "type": "uint256", "value": "_amount"}, "id": 3198, "name": "Identifier", "src": "3804:7:3"}], "id": 3199, "name": "FunctionCall", "src": "3787:25:3"}], "id": 3200, "name": "Assignment", "src": "3772:40:3"}], "id": 3201, "name": "ExpressionStatement", "src": "3772:40:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3202, "name": "Identifier", "src": "3822:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3186, "type": "address", "value": "_tokenOwner"}, "id": 3203, "name": "Identifier", "src": "3831:11:3"}], "id": 3204, "name": "IndexAccess", "src": "3822:21:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sub", "referencedDeclaration": 2978, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3205, "name": "Identifier", "src": "3846:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3186, "type": "address", "value": "_tokenOwner"}, "id": 3206, "name": "Identifier", "src": "3855:11:3"}], "id": 3207, "name": "IndexAccess", "src": "3846:21:3"}], "id": 3208, "name": "MemberAccess", "src": "3846:25:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3188, "type": "uint256", "value": "_amount"}, "id": 3209, "name": "Identifier", "src": "3872:7:3"}], "id": 3210, "name": "FunctionCall", "src": "3846:34:3"}], "id": 3211, "name": "Assignment", "src": "3822:58:3"}], "id": 3212, "name": "ExpressionStatement", "src": "3822:58:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 3056, "type": "function (address,uint256)", "value": "Burn"}, "id": 3213, "name": "Identifier", "src": "3895:4:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3186, "type": "address", "value": "_tokenOwner"}, "id": 3214, "name": "Identifier", "src": "3900:11:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3188, "type": "uint256", "value": "_amount"}, "id": 3215, "name": "Identifier", "src": "3913:7:3"}], "id": 3216, "name": "FunctionCall", "src": "3895:26:3"}], "id": 3217, "name": "EmitStatement", "src": "3890:31:3"}, {"attributes": {"functionReturnParameters": 3194}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 3218, "name": "Literal", "src": "3938:4:3"}], "id": 3219, "name": "Return", "src": "3931:11:3"}], "id": 3220, "name": "Block", "src": "3762:187:3"}], "id": 3221, "name": "FunctionDefinition", "src": "3656:293:3"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "get<PERSON>wner", "scope": 3338, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 3222, "name": "ParameterList", "src": "3972:2:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 3229, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3223, "name": "ElementaryTypeName", "src": "3996:7:3"}], "id": 3224, "name": "VariableDeclaration", "src": "3996:7:3"}], "id": 3225, "name": "ParameterList", "src": "3995:9:3"}, {"children": [{"attributes": {"functionReturnParameters": 3225}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3013, "type": "address", "value": "owner"}, "id": 3226, "name": "Identifier", "src": "4022:5:3"}], "id": 3227, "name": "Return", "src": "4015:12:3"}], "id": 3228, "name": "Block", "src": "4005:29:3"}], "id": 3229, "name": "FunctionDefinition", "src": "3955:79:3"}, {"attributes": {"documentation": null, "name": "only<PERSON><PERSON>er", "visibility": "internal"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 3230, "name": "ParameterList", "src": "4058:2:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "type": "function (bool) pure", "value": "require"}, "id": 3231, "name": "Identifier", "src": "4071:7:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "==", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4946, "type": "msg", "value": "msg"}, "id": 3232, "name": "Identifier", "src": "4079:3:3"}], "id": 3233, "name": "MemberAccess", "src": "4079:10:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3013, "type": "address", "value": "owner"}, "id": 3234, "name": "Identifier", "src": "4093:5:3"}], "id": 3235, "name": "BinaryOperation", "src": "4079:19:3"}], "id": 3236, "name": "FunctionCall", "src": "4071:28:3"}], "id": 3237, "name": "ExpressionStatement", "src": "4071:28:3"}, {"id": 3238, "name": "PlaceholderStatement", "src": "4109:1:3"}], "id": 3239, "name": "Block", "src": "4061:56:3"}], "id": 3240, "name": "ModifierDefinition", "src": "4040:77:3"}, {"attributes": {"documentation": null, "name": "canMint", "visibility": "internal"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 3241, "name": "ParameterList", "src": "4139:2:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "type": "function (bool) pure", "value": "require"}, "id": 3242, "name": "Identifier", "src": "4152:7:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!", "prefix": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3009, "type": "bool", "value": "mintingFinished"}, "id": 3243, "name": "Identifier", "src": "4161:15:3"}], "id": 3244, "name": "UnaryOperation", "src": "4160:16:3"}], "id": 3245, "name": "FunctionCall", "src": "4152:25:3"}], "id": 3246, "name": "ExpressionStatement", "src": "4152:25:3"}, {"id": 3247, "name": "PlaceholderStatement", "src": "4187:1:3"}], "id": 3248, "name": "Block", "src": "4142:53:3"}], "id": 3249, "name": "ModifierDefinition", "src": "4123:72:3"}, {"attributes": {"documentation": "@dev Transfer tokens from one address to another\n@param _from address The address which you want to send tokens from\n@param _to address The address which you want to transfer to\n@param _value uint256 the amount of tokens to be transferred", "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "transferFrom", "scope": 3338, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_from", "scope": 3337, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3250, "name": "ElementaryTypeName", "src": "4814:7:3"}], "id": 3251, "name": "VariableDeclaration", "src": "4814:13:3"}, {"attributes": {"constant": false, "name": "_to", "scope": 3337, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 3252, "name": "ElementaryTypeName", "src": "4837:7:3"}], "id": 3253, "name": "VariableDeclaration", "src": "4837:11:3"}, {"attributes": {"constant": false, "name": "_value", "scope": 3337, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3254, "name": "ElementaryTypeName", "src": "4858:7:3"}], "id": 3255, "name": "VariableDeclaration", "src": "4858:14:3"}], "id": 3256, "name": "ParameterList", "src": "4804:74:3"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 3337, "stateVariable": false, "storageLocation": "default", "type": "bool", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "bool", "type": "bool"}, "id": 3257, "name": "ElementaryTypeName", "src": "4895:4:3"}], "id": 3258, "name": "VariableDeclaration", "src": "4895:4:3"}], "id": 3259, "name": "ParameterList", "src": "4894:6:3"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4949, "type": "function (bool) pure", "value": "require"}, "id": 3260, "name": "Identifier", "src": "4911:7:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3253, "type": "address", "value": "_to"}, "id": 3261, "name": "Identifier", "src": "4919:3:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": true, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "address payable", "type_conversion": true}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "type": "type(address)", "value": "address"}, "id": 3262, "name": "ElementaryTypeNameExpression", "src": "4926:7:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 3263, "name": "Literal", "src": "4934:1:3"}], "id": 3264, "name": "FunctionCall", "src": "4926:10:3"}], "id": 3265, "name": "BinaryOperation", "src": "4919:17:3"}], "id": 3266, "name": "FunctionCall", "src": "4911:26:3"}], "id": 3267, "name": "ExpressionStatement", "src": "4911:26:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_ce42a17daeaf718c0f1b34bbe14453c7b1813857f0ec2c1766b69fda815bc31d", "typeString": "literal_string \"From doesn't have enough balance\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 3268, "name": "Identifier", "src": "4947:7:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3255, "type": "uint256", "value": "_value"}, "id": 3269, "name": "Identifier", "src": "4955:6:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3270, "name": "Identifier", "src": "4965:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3271, "name": "Identifier", "src": "4974:5:3"}], "id": 3272, "name": "IndexAccess", "src": "4965:15:3"}], "id": 3273, "name": "BinaryOperation", "src": "4955:25:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "46726f6d20646f65736e2774206861766520656e6f7567682062616c616e6365", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"From doesn't have enough balance\"", "value": "From doesn't have enough balance"}, "id": 3274, "name": "Literal", "src": "4982:34:3"}], "id": 3275, "name": "FunctionCall", "src": "4947:70:3"}], "id": 3276, "name": "ExpressionStatement", "src": "4947:70:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_f83afab5a2fdc018f2f2409afdd945b8b74c8752fac2171cdb87f94c6eb2b8be", "typeString": "literal_string \"Not allowed to spend this much\""}], "overloadedDeclarations": [4949, 4950], "referencedDeclaration": 4950, "type": "function (bool,string memory) pure", "value": "require"}, "id": 3277, "name": "Identifier", "src": "5027:7:3"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3255, "type": "uint256", "value": "_value"}, "id": 3278, "name": "Identifier", "src": "5048:6:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "mapping(address => uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3019, "type": "mapping(address => mapping(address => uint256))", "value": "allowed"}, "id": 3279, "name": "Identifier", "src": "5058:7:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3280, "name": "Identifier", "src": "5066:5:3"}], "id": 3281, "name": "IndexAccess", "src": "5058:14:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3282, "name": "Identifier", "src": "5073:2:3"}], "id": 3283, "name": "MemberAccess", "src": "5073:9:3"}], "id": 3284, "name": "IndexAccess", "src": "5058:25:3"}], "id": 3285, "name": "BinaryOperation", "src": "5048:35:3"}, {"attributes": {"argumentTypes": null, "hexvalue": "4e6f7420616c6c6f77656420746f207370656e642074686973206d756368", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Not allowed to spend this much\"", "value": "Not allowed to spend this much"}, "id": 3286, "name": "Literal", "src": "5097:32:3"}], "id": 3287, "name": "FunctionCall", "src": "5027:112:3"}], "id": 3288, "name": "ExpressionStatement", "src": "5027:112:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3289, "name": "Identifier", "src": "5150:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3290, "name": "Identifier", "src": "5159:5:3"}], "id": 3291, "name": "IndexAccess", "src": "5150:15:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sub", "referencedDeclaration": 2978, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3292, "name": "Identifier", "src": "5168:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3293, "name": "Identifier", "src": "5177:5:3"}], "id": 3294, "name": "IndexAccess", "src": "5168:15:3"}], "id": 3295, "name": "MemberAccess", "src": "5168:19:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3255, "type": "uint256", "value": "_value"}, "id": 3296, "name": "Identifier", "src": "5188:6:3"}], "id": 3297, "name": "FunctionCall", "src": "5168:27:3"}], "id": 3298, "name": "Assignment", "src": "5150:45:3"}], "id": 3299, "name": "ExpressionStatement", "src": "5150:45:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3300, "name": "Identifier", "src": "5205:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3253, "type": "address", "value": "_to"}, "id": 3301, "name": "Identifier", "src": "5214:3:3"}], "id": 3302, "name": "IndexAccess", "src": "5205:13:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "add", "referencedDeclaration": 3002, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3023, "type": "mapping(address => uint256)", "value": "balances"}, "id": 3303, "name": "Identifier", "src": "5221:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3253, "type": "address", "value": "_to"}, "id": 3304, "name": "Identifier", "src": "5230:3:3"}], "id": 3305, "name": "IndexAccess", "src": "5221:13:3"}], "id": 3306, "name": "MemberAccess", "src": "5221:17:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3255, "type": "uint256", "value": "_value"}, "id": 3307, "name": "Identifier", "src": "5239:6:3"}], "id": 3308, "name": "FunctionCall", "src": "5221:25:3"}], "id": 3309, "name": "Assignment", "src": "5205:41:3"}], "id": 3310, "name": "ExpressionStatement", "src": "5205:41:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "mapping(address => uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3019, "type": "mapping(address => mapping(address => uint256))", "value": "allowed"}, "id": 3311, "name": "Identifier", "src": "5256:7:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3312, "name": "Identifier", "src": "5264:5:3"}], "id": 3315, "name": "IndexAccess", "src": "5256:14:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3313, "name": "Identifier", "src": "5271:2:3"}], "id": 3314, "name": "MemberAccess", "src": "5271:9:3"}], "id": 3316, "name": "IndexAccess", "src": "5256:25:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "uint256", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sub", "referencedDeclaration": 2978, "type": "function (uint256,uint256) pure returns (uint256)"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "mapping(address => uint256)"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3019, "type": "mapping(address => mapping(address => uint256))", "value": "allowed"}, "id": 3317, "name": "Identifier", "src": "5284:7:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3318, "name": "Identifier", "src": "5292:5:3"}], "id": 3319, "name": "IndexAccess", "src": "5284:14:3"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "origin", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 4958, "type": "tx", "value": "tx"}, "id": 3320, "name": "Identifier", "src": "5299:2:3"}], "id": 3321, "name": "MemberAccess", "src": "5299:9:3"}], "id": 3322, "name": "IndexAccess", "src": "5284:25:3"}], "id": 3323, "name": "MemberAccess", "src": "5284:29:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3255, "type": "uint256", "value": "_value"}, "id": 3324, "name": "Identifier", "src": "5314:6:3"}], "id": 3325, "name": "FunctionCall", "src": "5284:37:3"}], "id": 3326, "name": "Assignment", "src": "5256:65:3"}], "id": 3327, "name": "ExpressionStatement", "src": "5256:65:3"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 3042, "type": "function (address,address,uint256)", "value": "Transfer"}, "id": 3328, "name": "Identifier", "src": "5336:8:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3251, "type": "address", "value": "_from"}, "id": 3329, "name": "Identifier", "src": "5345:5:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3253, "type": "address", "value": "_to"}, "id": 3330, "name": "Identifier", "src": "5352:3:3"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 3255, "type": "uint256", "value": "_value"}, "id": 3331, "name": "Identifier", "src": "5357:6:3"}], "id": 3332, "name": "FunctionCall", "src": "5336:28:3"}], "id": 3333, "name": "EmitStatement", "src": "5331:33:3"}, {"attributes": {"functionReturnParameters": 3259}, "children": [{"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 3334, "name": "Literal", "src": "5381:4:3"}], "id": 3335, "name": "Return", "src": "5374:11:3"}], "id": 3336, "name": "Block", "src": "4901:491:3"}], "id": 3337, "name": "FunctionDefinition", "src": "4783:609:3"}], "id": 3338, "name": "ContractDefinition", "src": "1368:5575:3"}], "id": 3339, "name": "SourceUnit", "src": "0:6944:3"}, "compiler": {"name": "solc", "version": "0.5.17+commit.d19bba13.Emscripten.clang"}, "networks": {}, "schemaVersion": "3.4.16", "updatedAt": "2025-06-23T09:15:20.610Z", "devdoc": {"details": "Math operations with safety checks that throw on error", "methods": {}, "title": "SafeMath"}, "userdoc": {"methods": {}}}